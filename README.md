# a3i.ai

A modern platform for AI-powered image and video generation with an intuitive interface.

## Project Overview

a3i.ai is an advanced AI platform that offers powerful tools for creating stunning images and videos. The platform features a dark-themed, responsive interface with beautiful gradients and animations, making creative AI accessible to everyone.

## Features

- **AI Image Generator**: Create original images in seconds from text prompts
- **Image to Text Converter**: Transform images into editable text or AI-generated captions
- **AI Avatar Creator**: Generate personalized avatars with consistent style
- **Multi-language Support**: Available in English and Chinese
- **Responsive Design**: Optimized for all devices from mobile to desktop
- **Dark Mode**: Beautiful dark theme with electric blue and neon purple accents
- **IP-Based Access Control**: Restrict access to specific IP addresses
- **Bot Protection**: Prevent web crawlers from indexing your content

## Technology Stack

- **Frontend**: Next.js 15, React 18, TailwindCSS
- **Internationalization**: next-intl
- **Animation**: Framer Motion
- **Database**: PostgreSQL, Prisma
- **Payment Processing**: Stripe
- **Authentication**: NextAuth.js

## Project Structure

```
/
├── app/                    # Next.js app directory
│   └── [locale]/           # Internationalized routes
│       ├── privacy-policy/ # Privacy policy page
│       └── terms-of-service/ # Terms of service page
├── components/             # React components
│   └── ui/                 # UI components
├── hooks/                  # Custom React hooks
├── i18n/                   # Internationalization configuration
├── locales/                # Translation files
├── prisma/                 # Prisma schema and migrations
│   ├── schema.prisma
│   └── migrations/
├── public/                 # Static files
├── utils/                  # Utility functions
```

## Environment Variables

Create a `.env` file in the root directory and add the following variables:

```
# Application Settings
NODE_ENV=development
NEXT_PUBLIC_APP_NAME="a3i.ai"
NEXT_PUBLIC_APP_DESCRIPTION="Advanced AI tools for generating videos and images with ease"
NEXT_PUBLIC_SITE_URL=http://localhost:3000

# Database
DATABASE_URL="postgresql://username:password@localhost:5432/a3i?schema=public"

# Authentication
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your_secret_key

# Access Control
# 用逗号分隔多个IP (例如: 127.0.0.1,***********)
# 支持CIDR表示法 (例如: ***********/24)
ALLOWED_IPS=127.0.0.1,***********/24

# AI Service API Keys
OPENAI_API_KEY=your_openai_api_key
STABILITY_API_KEY=your_stability_api_key
REPLICATE_API_KEY=your_replicate_api_key

# Analytics (Optional)
NEXT_PUBLIC_GA_ID=your_google_analytics_id
```

## Getting Started

1. Clone the repository:
```bash
git clone https://github.com/yourusername/a3i.git
cd a3i
```

2. Install dependencies:
```bash
npm install
```

3. Set up the database:
```bash
npx prisma migrate dev
```

4. Configure access control in your `.env` file:
```
# 如果在开发环境，可以省略此配置，所有IP都可访问
# 生产环境中，设置允许访问的IP地址列表
ALLOWED_IPS=***********,10.0.0.0/24
```

5. Run the development server:
```bash
npm run dev
```

6. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Access Control

The project includes built-in protection to:

1. **Restrict Access by IP**: Only IPs listed in the `ALLOWED_IPS` environment variable can access the site
2. **Block Web Crawlers**: Bots are automatically blocked with 403 responses
3. **SEO Protection**: All pages include headers to prevent indexing

### How to Allow Access

- **Development Mode**: In development mode (`NODE_ENV=development`), all IPs are allowed by default
- **Production**: In production, you must specify allowed IPs in the `ALLOWED_IPS` environment variable
- **Empty Value**: If `ALLOWED_IPS` is empty, access is not restricted by IP

## Internationalization

The application supports multiple languages:

- English (default)
- Chinese (Simplified)

To add a new language:
1. Add the locale to `locales/index.ts`
2. Create corresponding translation files in the `locales` directory
3. Ensure UI components adapt to the text length changes

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- UI design inspired by modern AI tools and creative platforms
- Font system using Space Grotesk, Plus Jakarta Sans, Inter, and JetBrains Mono
