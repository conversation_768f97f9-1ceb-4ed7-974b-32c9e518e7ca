import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/utils/auth'
import { CreditsService } from '@/utils/credits'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { requiredAmount } = await request.json()

    if (!requiredAmount || requiredAmount <= 0) {
      return NextResponse.json({ error: 'Invalid required amount' }, { status: 400 })
    }

    const currentCredits = await CreditsService.getUserCredits(session.user.id)
    const hasEnough = await CreditsService.hasEnoughCredits(session.user.id, requiredAmount)

    return NextResponse.json({ 
      currentCredits,
      requiredAmount,
      hasEnough,
      shortfall: hasEnough ? 0 : requiredAmount - currentCredits
    })
  } catch (error) {
    console.error('Error checking credits:', error)
    return NextResponse.json({ 
      error: 'Internal server error' 
    }, { status: 500 })
  }
}
