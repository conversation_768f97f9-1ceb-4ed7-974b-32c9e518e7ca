import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/utils/auth'
import { CreditsService } from '@/utils/credits'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { amount, description, reference } = await request.json()

    if (!amount || amount <= 0) {
      return NextResponse.json({ error: 'Invalid amount' }, { status: 400 })
    }

    if (!description) {
      return NextResponse.json({ error: 'Description is required' }, { status: 400 })
    }

    const result = await CreditsService.deductCredits(
      session.user.id,
      amount,
      description,
      reference
    )

    if (!result.success) {
      return NextResponse.json({ 
        error: result.error 
      }, { 
        status: result.error === 'Insufficient credits' ? 402 : 500 
      })
    }

    return NextResponse.json({ 
      success: true,
      newBalance: result.newBalance 
    })
  } catch (error) {
    console.error('Error deducting credits:', error)
    return NextResponse.json({ 
      error: 'Internal server error' 
    }, { status: 500 })
  }
}
