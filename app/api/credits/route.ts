import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/utils/auth'
import { prisma } from '@/utils/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { credits: true }
    })

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    return NextResponse.json({ credits: user.credits })
  } catch (error) {
    console.error('Error fetching credits:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { amount, type, description, reference } = await request.json()

    if (!amount || !type) {
      return NextResponse.json({ error: 'Amount and type are required' }, { status: 400 })
    }

    // 开始事务
    const result = await prisma.$transaction(async (tx) => {
      // 获取当前用户
      const user = await tx.user.findUnique({
        where: { id: session.user.id },
        select: { credits: true }
      })

      if (!user) {
        throw new Error('User not found')
      }

      // 检查余额是否足够（如果是消费操作）
      if (amount < 0 && user.credits + amount < 0) {
        throw new Error('Insufficient credits')
      }

      // 更新用户credits
      const updatedUser = await tx.user.update({
        where: { id: session.user.id },
        data: { credits: user.credits + amount },
        select: { credits: true }
      })

      // 创建交易记录
      await tx.creditTransaction.create({
        data: {
          userId: session.user.id,
          type,
          amount,
          description,
          reference
        }
      })

      return updatedUser
    })

    return NextResponse.json({ credits: result.credits })
  } catch (error) {
    console.error('Error updating credits:', error)
    return NextResponse.json({ 
      error: error instanceof Error ? error.message : 'Internal server error' 
    }, { status: 500 })
  }
}
