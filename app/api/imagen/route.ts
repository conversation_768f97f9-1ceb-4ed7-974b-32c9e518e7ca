import { NextRequest, NextResponse } from 'next/server';
import { VertexAI } from '@google-cloud/vertexai';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/utils/auth';
import { CreditsService, CREDIT_COSTS } from '@/utils/credits';
import * as fs from 'fs';
import * as path from 'path';
import { v4 as uuidv4 } from 'uuid';
import { GoogleAuth } from 'google-auth-library';

// Initialize Vertex with your Cloud project and location
const projectId = 'projectatm-89b3a';
const location = 'us-central1';
const DEFAULT_NEGATIVE_PROMPT = "poor quality, low resolution, bad anatomy, worst quality, low quality";

// Set the credentials path for authentication
process.env.GOOGLE_APPLICATION_CREDENTIALS = path.join(process.cwd(), 'google_application_credentials.json');

/**
 * Enhance prompt based on selected style
 */
function enhancePromptWithStyle(prompt: string, style: string): string {
  const styleEnhancements = {
    photorealistic: 'photorealistic, high quality, detailed, professional photography',
    artistic: 'artistic style, creative, expressive, fine art',
    illustration: 'illustration style, digital art, clean lines, vibrant colors',
    abstract: 'abstract art, conceptual, modern, artistic interpretation',
    fantasy: 'fantasy art, magical, imaginative, epic, detailed fantasy illustration'
  };

  const enhancement = styleEnhancements[style as keyof typeof styleEnhancements] || styleEnhancements.photorealistic;
  return `${prompt}, ${enhancement}`;
}

/**
 * Helper function to log response data without image base64 content
 */
function logResponseWithoutImageData(responseData: any) {
  // Create a deep copy of the response
  const sanitizedResponse = JSON.parse(JSON.stringify(responseData));

  // Remove base64 image data from logs
  if (sanitizedResponse.predictions && Array.isArray(sanitizedResponse.predictions)) {
    sanitizedResponse.predictions.forEach((prediction: any) => {
      if (prediction.bytesBase64Encoded) {
        prediction.bytesBase64Encoded = '[BASE64_IMAGE_DATA_REMOVED]';
      }
      if (prediction.b64) {
        prediction.b64 = '[BASE64_IMAGE_DATA_REMOVED]';
      }
      if (prediction.image) {
        prediction.image = '[BASE64_IMAGE_DATA_REMOVED]';
      }
    });
  }

  return sanitizedResponse;
}

export async function POST(req: Request) {
  const requestId = uuidv4().substring(0, 8);
  console.log(`[${requestId}] Processing image generation request`);

  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      console.log(`[${requestId}] Error: Unauthorized`);
      return Response.json({ success: false, error: 'Authentication required' }, { status: 401 });
    }

    // Check credits before processing
    const hasEnoughCredits = await CreditsService.hasEnoughCredits(session.user.id, CREDIT_COSTS.IMAGE_GENERATION);
    if (!hasEnoughCredits) {
      console.log(`[${requestId}] Error: Insufficient credits`);
      return Response.json({
        success: false,
        error: 'Insufficient credits',
        requiredCredits: CREDIT_COSTS.IMAGE_GENERATION
      }, { status: 402 });
    }

    const body = await req.json();
    const { prompt, style = 'photorealistic', aspectRatio = 'square', negativePrompt = DEFAULT_NEGATIVE_PROMPT } = body;

    if (!prompt) {
      console.log(`[${requestId}] Error: Missing prompt`);
      return Response.json({ success: false, error: 'Prompt is required' }, { status: 400 });
    }

    // Map aspect ratio to image dimensions
    const aspectRatioMap = {
      square: { width: 1024, height: 1024 },
      portrait: { width: 768, height: 1024 },
      landscape: { width: 1024, height: 768 },
      wide: { width: 1344, height: 768 },
    };

    const dimensions = aspectRatioMap[aspectRatio as keyof typeof aspectRatioMap] || aspectRatioMap.square;

    // Enhance prompt based on style
    const styleEnhancedPrompt = enhancePromptWithStyle(prompt, style);

    try {
      console.log(`[${requestId}] Generating image with prompt: "${prompt.substring(0, 50)}${prompt.length > 50 ? '...' : ''}"`);

      const auth = new GoogleAuth({
        scopes: ['https://www.googleapis.com/auth/cloud-platform'],
      });
      const client = await auth.getClient();
      const accessToken = await client.getAccessToken();

      if (!accessToken?.token) {
        console.error(`[${requestId}] Failed to obtain access token`);
        return Response.json({
          success: false,
          error: 'Authentication failed. Could not obtain access token.'
        }, { status: 500 });
      }

      if (!projectId) {
        console.error(`[${requestId}] Missing Google Cloud Project ID`);
        return Response.json({
          success: false,
          error: 'Server configuration error: Missing Google Cloud Project ID'
        }, { status: 500 });
      }

      const url = `https://${location}-aiplatform.googleapis.com/v1/projects/${projectId}/locations/${location}/publishers/google/models/imagegeneration@002:predict`;

      // Prepare request body for Vertex AI
      const request = {
        instances: [
          {
            prompt: styleEnhancedPrompt,
            negative_prompt: negativePrompt,
          }
        ],
        parameters: {
          sampleCount: 1,
          sampleImageSize: "1024"
        }
      };

      console.log(`[${requestId}] Sending request to Vertex AI`);

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${accessToken.token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`[${requestId}] Vertex AI API error: ${response.status} - ${errorText}`);
        return Response.json({
          success: false,
          error: `Vertex AI API error: ${response.status} - ${errorText}`
        }, { status: response.status });
      }

      const responseData = await response.json();
      console.log(`[${requestId}] Vertex AI response received:`,
        logResponseWithoutImageData(responseData));

      // Extract image data from response
      let imageBase64 = null;

      // Extract image data based on response structure
      if (responseData.predictions && responseData.predictions[0]) {
        if (responseData.predictions[0].bytesBase64Encoded) {
          imageBase64 = responseData.predictions[0].bytesBase64Encoded;
        } else if (responseData.predictions[0].b64) {
          imageBase64 = responseData.predictions[0].b64;
        }
      }

      if (!imageBase64) {
        console.error(`[${requestId}] No image data found in response structure:`,
          JSON.stringify(Object.keys(responseData)));
        return Response.json({
          success: false,
          error: 'Failed to extract image data from API response'
        }, { status: 500 });
      }

      // Create directory if it doesn't exist
      const directory = path.join(process.cwd(), 'public', 'generated');
      fs.mkdirSync(directory, { recursive: true });

      // Ensure directory has correct permissions
      try {
        fs.chmodSync(directory, 0o777);
      } catch (error) {
        console.warn(`[${requestId}] Could not set directory permissions:`, error);
      }

      // Save the image with a unique filename
      const filename = `${uuidv4()}.png`;
      const filePath = path.join(directory, filename);

      // Make sure the URL path is correct - this is what the frontend will use
      const imageUrl = `/generated/${filename}`;

      console.log(`[${requestId}] Saving image (${Math.round(imageBase64.length / 1024)}KB) to ${filePath}`);

      // Write the image to disk with proper permissions
      const imageBuffer = Buffer.from(imageBase64, 'base64');
      fs.writeFileSync(filePath, imageBuffer);

      // Ensure file has correct permissions
      try {
        fs.chmodSync(filePath, 0o666);
      } catch (error) {
        console.warn(`[${requestId}] Could not set file permissions:`, error);
      }

      console.log(`[${requestId}] Successfully generated and saved image: ${imageUrl}`);

      // For debugging, check if the file exists after saving
      const fileExists = fs.existsSync(filePath);
      console.log(`[${requestId}] File exists check: ${fileExists}, Size: ${fileExists ? fs.statSync(filePath).size : 0} bytes`);

      // Also include a data URL as a fallback
      const dataUrl = `data:image/png;base64,${imageBase64}`;

      // Return both the standard path and a data URL as fallback
      return Response.json({
        success: true,
        imageUrl,
        dataUrl,
        prompt: styleEnhancedPrompt,
        style,
        aspectRatio,
        dimensions
      });
    } catch (error: any) {
      console.error(`[${requestId}] Error generating image:`, error);
      return Response.json({
        success: false,
        error: error.message || 'Failed to generate image'
      }, { status: 500 });
    }
  } catch (error: any) {
    console.error(`[${requestId}] Error parsing request:`, error);
    return Response.json({
      success: false,
      error: 'Invalid request format'
    }, { status: 400 });
  }
}

export async function GET() {
  return NextResponse.json({ message: 'Imagen API is running' });
}