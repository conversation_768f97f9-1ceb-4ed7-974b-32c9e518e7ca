import { NextRequest, NextResponse } from 'next/server';
import { GoogleAuth } from 'google-auth-library';
import * as path from 'path';

// Use the same configuration as imagen API
const projectId = 'projectatm-89b3a';
const location = 'us-central1';

// Set the credentials path for authentication
process.env.GOOGLE_APPLICATION_CREDENTIALS = path.join(process.cwd(), 'google_application_credentials.json');

export async function POST(request: NextRequest) {
    try {
        // Check if the request has the correct content type
        const contentType = request.headers.get('content-type');
        if (!contentType || !contentType.includes('multipart/form-data')) {
            return NextResponse.json(
                { success: false, error: 'Content-Type must be multipart/form-data' },
                { status: 400 }
            );
        }

        // Parse form data
        const formData = await request.formData();
        const imageFile = formData.get('image') as File;
        const prompt = formData.get('prompt') as string || 'Describe this image in detail.';
        const language = formData.get('language') as string || 'en';

        if (!imageFile) {
            return NextResponse.json(
                { success: false, error: 'No image file provided' },
                { status: 400 }
            );
        }

        // Validate file type
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
        if (!allowedTypes.includes(imageFile.type)) {
            return NextResponse.json(
                { success: false, error: 'Invalid file type. Please upload a JPEG, PNG, or WebP image.' },
                { status: 400 }
            );
        }

        // Validate file size (max 10MB)
        const maxSize = 10 * 1024 * 1024; // 10MB
        if (imageFile.size > maxSize) {
            return NextResponse.json(
                { success: false, error: 'File size too large. Maximum size is 10MB.' },
                { status: 400 }
            );
        }

        // Convert image to base64
        const imageBuffer = await imageFile.arrayBuffer();
        const imageBase64 = Buffer.from(imageBuffer).toString('base64');

        // Create language-specific prompts
        const languagePrompts = {
            en: prompt,
            zh: language === 'zh' ? '请详细描述这张图片的内容。' : prompt,
        };

        const finalPrompt = languagePrompts[language as keyof typeof languagePrompts] || prompt;

        // Use GoogleAuth for authentication (same as imagen API)
        const auth = new GoogleAuth({
            scopes: ['https://www.googleapis.com/auth/cloud-platform'],
        });
        const client = await auth.getClient();
        const accessToken = await client.getAccessToken();

        if (!accessToken?.token) {
            console.error('Failed to obtain access token');
            return NextResponse.json({
                success: false,
                error: 'Authentication failed. Could not obtain access token.'
            }, { status: 500 });
        }

        if (!projectId) {
            console.error('Missing Google Cloud Project ID');
            return NextResponse.json({
                success: false,
                error: 'Server configuration error: Missing Google Cloud Project ID'
            }, { status: 500 });
        }

        // Call Gemini API directly using REST
        const url = `https://${location}-aiplatform.googleapis.com/v1/projects/${projectId}/locations/${location}/publishers/google/models/gemini-1.5-pro:generateContent`;

        const requestBody = {
            contents: [{
                role: 'user',
                parts: [
                    {
                        text: finalPrompt,
                    },
                    {
                        inline_data: {
                            mime_type: imageFile.type,
                            data: imageBase64,
                        },
                    },
                ]
            }],
            generationConfig: {
                temperature: 0.4,
                topK: 32,
                topP: 1,
                maxOutputTokens: 2048,
            },
        };

        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${accessToken.token}`,
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestBody),
        });

        if (!response.ok) {
            const errorText = await response.text();
            console.error('Gemini API error:', errorText);
            return NextResponse.json(
                { success: false, error: 'Failed to analyze image. Please try again.' },
                { status: 500 }
            );
        }

        const result = await response.json();
        const text = result.candidates?.[0]?.content?.parts?.[0]?.text;

        if (!text) {
            return NextResponse.json(
                { success: false, error: 'Failed to generate description' },
                { status: 500 }
            );
        }

        // Return the result
        return NextResponse.json({
            success: true,
            description: text,
            prompt: finalPrompt,
            language: language,
            fileName: imageFile.name,
            fileSize: imageFile.size,
            mimeType: imageFile.type,
        });

    } catch (error) {
        console.error('Error in image-to-text API:', error);
        
        // Handle specific Vertex AI errors
        if (error instanceof Error) {
            if (error.message.includes('quota')) {
                return NextResponse.json(
                    { success: false, error: 'API quota exceeded. Please try again later.' },
                    { status: 429 }
                );
            }
            if (error.message.includes('permission')) {
                return NextResponse.json(
                    { success: false, error: 'Authentication failed. Please check API credentials.' },
                    { status: 401 }
                );
            }
        }

        return NextResponse.json(
            { success: false, error: 'Internal server error. Please try again.' },
            { status: 500 }
        );
    }
}

// Handle OPTIONS request for CORS
export async function OPTIONS() {
    return new NextResponse(null, {
        status: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'POST, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type',
        },
    });
}
