import { Inter, Space_Grotesk, Plus_Jakarta_Sans, JetBrains_Mono } from "next/font/google";
import { NextIntlClientProvider } from 'next-intl';
import { getMessages, setRequestLocale, getTranslations } from 'next-intl/server';
import "./globals.css";
import Header from "@/components/ui/Header";
import Footer from "@/components/ui/Footer"
import Script from 'next/script';
import { Locale, locales } from '@/i18n/routing';
import { ReactNode } from 'react';
import { getBaseUrl } from '@/utils/metadata';
import PageViewTracker from "@/components/PageViewTracker";
import AuthProvider from "@/components/providers/AuthProvider";

// Import the Google Fonts
const inter = Inter({
    subsets: ["latin"],
    display: 'swap',
    variable: '--font-inter',
});

const spaceGrotesk = Space_Grotesk({
    subsets: ["latin"],
    display: 'swap',
    variable: '--font-space-grotesk',
});

const plusJakartaSans = Plus_Jakarta_Sans({
    subsets: ["latin"],
    display: 'swap',
    variable: '--font-plus-jakarta-sans',
});

const jetbrainsMono = JetBrains_Mono({
    subsets: ["latin"],
    display: 'swap',
    variable: '--font-jetbrains-mono',
});

// We'll combine all font variables
const fontVariables = `${inter.variable} ${spaceGrotesk.variable} ${plusJakartaSans.variable} ${jetbrainsMono.variable}`;

const GoogleAnalytics = () => {
    const gaId = process.env.NEXT_PUBLIC_GA_ID;
    const gtagId = process.env.NEXT_PUBLIC_GTAG_ID;

    if (!gaId && !gtagId) return null;

    return (
        <>
            {gaId && (
                <>
                    <Script
                        src={`https://www.googletagmanager.com/gtag/js?id=${gaId}`}
                        strategy="afterInteractive"
                    />
                    <Script id="ga-script" strategy="afterInteractive">
                        {`
                            window.dataLayer = window.dataLayer || [];
                            function gtag(){dataLayer.push(arguments);}
                            gtag('js', new Date());
                            gtag('config', '${gaId}', {
                                send_page_view: false
                            });
                            
                            if (typeof window !== 'undefined') {
                                window.addEventListener('routeChangeComplete', (url) => {
                                    gtag('event', 'page_view', {
                                        page_path: url
                                    });
                                });
                            }
                        `}
                    </Script>
                </>
            )}
            {gtagId && (
                <>
                    <Script
                        src={`https://www.googletagmanager.com/gtag/js?id=${gtagId}`}
                        strategy="afterInteractive"
                    />
                    <Script id="gtag-script" strategy="afterInteractive">
                        {`
                            window.dataLayer = window.dataLayer || [];
                            function gtag(){dataLayer.push(arguments);}
                            gtag('js', new Date());
                            gtag('config', '${gtagId}');
                        `}
                    </Script>
                </>
            )}
        </>
    );
};

// 添加新的组件用于加载广告脚本
const AdScript = () => {
    return (
        <Script
            src="https://alwingulla.com/88/tag.min.js"
            data-zone="118915"
            async
            data-cfasync="false"
            strategy="afterInteractive"
        />
    );
};

type Props = {
    children: ReactNode;
    params: { locale: string } | Promise<{ locale: string }>;
};

export function generateStaticParams() {
    return locales.map((locale) => ({ locale }));
}

export async function generateMetadata({ params: { locale } }: { params: { locale: string } }) {
    const baseUrl = await getBaseUrl();
    const t = await getTranslations('site');

    const alternateLinks: Record<string, string> = {};
    locales.forEach(lang => {
        const path = lang === 'en' ? '' : `/${lang}`;
        alternateLinks[lang] = `${baseUrl}${path}`;
    });

    return {
        title: {
            default: t('title'),
            template: `%s | ${t('title')}`
        },
        description: t('description'),
        keywords: t('keywords'),
        metadataBase: new URL(baseUrl),
        alternates: {
            canonical: alternateLinks[locale],
            languages: alternateLinks
        },
        openGraph: {
            title: t('title'),
            description: t('description'),
            type: "website",
            url: alternateLinks[locale],
            locale: locale,
            alternateLocale: locales.filter(l => l !== locale),
            images: [
                {
                    url: '/hero-background-ai-abstract-blue-purple-gradient.webp',
                    width: 1200,
                    height: 630,
                    alt: 'Futuristic dark AI-themed hero background with glowing grid and blue-purple gradient'
                }
            ]
        },
        twitter: {
            card: "summary_large_image",
            title: t('title'),
            description: t('description'),
            images: ['/hero-background-ai-abstract-blue-purple-gradient.webp']
        },
        other: {
            monetag: 'a214e8c629f8bdc9588e1e61d7128335',
            'google-adsense-account': 'ca-pub-****************'
        }
    };
}

export default async function LocaleLayout({
    children,
    params,
}: Props) {
    // Resolve params
    const resolvedParams = await (Promise.resolve(params));
    const locale = String(resolvedParams.locale);

    // Enable static rendering
    setRequestLocale(locale);

    // Get messages for the locale
    const messages = await getMessages();

    return (
        <html lang={locale} className={`dark ${fontVariables}`}>
            <head>
                <link rel="preconnect" href="https://fonts.googleapis.com" />
                <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
                <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet" />
            </head>
            <body className="min-h-screen flex flex-col bg-background-dark text-text-light font-sans antialiased">
                <AuthProvider>
                    <NextIntlClientProvider messages={messages}>
                        <Header />
                        <main className="flex-1 flex flex-col">
                            {children}
                        </main>
                        <Footer />
                        <PageViewTracker />
                    </NextIntlClientProvider>
                </AuthProvider>
                <GoogleAnalytics />
                {/* <AdScript /> */}
            </body>
        </html>
    );
}