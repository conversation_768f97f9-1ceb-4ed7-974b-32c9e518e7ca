'use client';

import { useState, useEffect, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { Link, Pathnames } from '@/i18n/routing';
import Image from 'next/image';
import { Palette, FileText, User } from 'lucide-react';

interface Tool {
    key: string;
}

interface HomeContentProps {
    tools: Tool[];
}

export default function HomeContent({ tools }: HomeContentProps) {
    const t = useTranslations('pages.home.content');
    const router = useRouter();
    const [prompt, setPrompt] = useState('');
    const [isClient, setIsClient] = useState(false);
    const [randomStarsConfig, setRandomStarsConfig] = useState<Array<{size: string, top: string, left: string, delay: string}>>([]);
    const [randomShootingStarsConfig, setRandomShootingStarsConfig] = useState<Array<{top: string, left: string, width: string, duration: string, delay: string, distance: string}>>([]);

    // 生成随机星星配置的函数
    const generateRandomStarsConfig = () => {
        const stars = [];
        for (let i = 0; i < 60; i++) {
            const sizeRand = Math.random();
            const size = sizeRand > 0.6 ? (sizeRand > 0.8 ? 'star-2' : 'star-3') : 'star-1';
            const top = `${Math.random() * 100}%`;
            const left = `${Math.random() * 100}%`;
            const delay = `${Math.random() * 5}s`;
            stars.push({ size, top, left, delay });
        }
        return stars;
    };

    // 生成随机流星配置的函数
    const generateRandomShootingStarsConfig = () => {
        const shootingStars = [];
        for (let i = 0; i < 5; i++) {
            const top = `${Math.random() * 70}%`;
            const left = `${Math.random() * 70}%`;
            const width = `${80 + Math.random() * 100}px`;
            const duration = `${4 + Math.random() * 8}s`;
            const delay = `${i * 4 + Math.random() * 5}s`;
            const distance = `${150 + Math.random() * 200}px`;
            shootingStars.push({ top, left, width, duration, delay, distance });
        }
        return shootingStars;
    };

    // 使用固定的星星配置避免水合错误（作为后备）
    const starsConfig = useMemo(() => {
        // 使用固定的种子生成一致的星星配置
        const stars = [];
        for (let i = 0; i < 60; i++) {
            // 使用索引作为种子生成伪随机值
            const seed1 = (i * 9301 + 49297) % 233280;
            const seed2 = (i * 1234 + 5678) % 100000;
            const seed3 = (i * 4321 + 8765) % 100000;
            const seed4 = (i * 2468 + 1357) % 50000;

            const sizeRand = (seed1 / 233280);
            const size = sizeRand > 0.6 ? (sizeRand > 0.8 ? 'star-2' : 'star-3') : 'star-1';
            const top = `${(seed2 / 100000) * 100}%`;
            const left = `${(seed3 / 100000) * 100}%`;
            const delay = `${(seed4 / 50000) * 5}s`;

            stars.push({ size, top, left, delay });
        }
        return stars;
    }, []);

    const shootingStarsConfig = useMemo(() => {
        const shootingStars = [];
        for (let i = 0; i < 5; i++) {
            const seed1 = (i * 7919 + 12345) % 70000;
            const seed2 = (i * 3571 + 98765) % 70000;
            const seed3 = (i * 8642 + 24680) % 100000;
            const seed4 = (i * 1357 + 97531) % 80000;
            const seed5 = (i * 9876 + 54321) % 50000;
            const seed6 = (i * 2468 + 13579) % 200000;

            const top = `${(seed1 / 70000) * 70}%`;
            const left = `${(seed2 / 70000) * 70}%`;
            const width = `${80 + (seed3 / 100000) * 100}px`;
            const duration = `${4 + (seed4 / 80000) * 8}s`;
            const delay = `${i * 4 + (seed5 / 50000) * 5}s`;
            const distance = `${150 + (seed6 / 200000) * 200}px`;

            shootingStars.push({ top, left, width, duration, delay, distance });
        }
        return shootingStars;
    }, []);

    useEffect(() => {
        setIsClient(true);
        // 在客户端挂载后生成随机配置
        setRandomStarsConfig(generateRandomStarsConfig());
        setRandomShootingStarsConfig(generateRandomShootingStarsConfig());
    }, []);

    // 添加键盘事件监听器用于重新生成星星（开发者功能）
    useEffect(() => {
        const handleKeyPress = (event: KeyboardEvent) => {
            // 按 Ctrl+Shift+R 重新生成星星位置
            if (event.ctrlKey && event.shiftKey && event.key === 'R') {
                event.preventDefault();
                regenerateStars();
                console.log('Stars regenerated! 🌟');
            }
        };

        if (isClient) {
            window.addEventListener('keydown', handleKeyPress);
            return () => window.removeEventListener('keydown', handleKeyPress);
        }
    }, [isClient]);

    // 可选：重新生成星星位置的函数（用于演示随机效果）
    const regenerateStars = () => {
        setRandomStarsConfig(generateRandomStarsConfig());
        setRandomShootingStarsConfig(generateRandomShootingStarsConfig());
    };

    // Icon mapping for tools
    const getIconComponent = (toolKey: string) => {
        switch (toolKey) {
            case 'imageGenerator':
                return Palette;
            case 'imageToText':
                return FileText;
            case 'avatarCreator':
                return User;
            default:
                return Palette;
        }
    };

    const handleGenerate = () => {
        if (!prompt.trim()) return;
        
        // Navigate to image generator with the prompt as URL parameter
        const encodedPrompt = encodeURIComponent(prompt.trim());
        router.push(`/image-generator?prompt=${encodedPrompt}`);
    };

    const handleKeyPress = (e: React.KeyboardEvent) => {
        if (e.key === 'Enter') {
            handleGenerate();
        }
    };

    return (
        <>
            {/* Hero Section */}
            <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
                {/* Hero Background Image */}
                <div className="absolute inset-0 z-0">
                    <Image
                        src="/hero-background-ai-abstract-blue-purple-gradient.webp"
                        alt="Futuristic dark AI-themed hero background with glowing grid and blue-purple gradient"
                        title="AI Background - Abstract Futuristic Grid with Blue and Purple Glow | a3i.ai"
                        fill
                        priority
                        className="object-cover object-center"
                        quality={90}
                        sizes="100vw"
                    />
                    <div className="absolute inset-0 bg-background-dark/30 backdrop-blur-[1px]"></div>

                    {/* Stars Animation - Only render on client to avoid hydration issues */}
                    {isClient && (
                        <div className="absolute inset-0 overflow-hidden">
                            {/* Random Stars configuration - 使用随机位置 */}
                            {randomStarsConfig.length > 0 ? randomStarsConfig.map((star, index) => (
                                <div
                                    key={`random-star-${index}`}
                                    className={`star ${star.size}`}
                                    style={{
                                        top: star.top,
                                        left: star.left,
                                        animationDelay: star.delay
                                    }}
                                ></div>
                            )) :
                            // 后备方案：使用固定配置
                            starsConfig.map((star, index) => (
                                <div
                                    key={`fallback-star-${index}`}
                                    className={`star ${star.size}`}
                                    style={{
                                        top: star.top,
                                        left: star.left,
                                        animationDelay: star.delay
                                    }}
                                ></div>
                            ))}

                            {/* Random Shooting Stars - 使用随机位置 */}
                            {randomShootingStarsConfig.length > 0 ? randomShootingStarsConfig.map((shootingStar, index) => (
                                <div
                                    key={`random-shooting-${index}`}
                                    className="shooting-star"
                                    style={{
                                        top: shootingStar.top,
                                        left: shootingStar.left,
                                        width: shootingStar.width,
                                        '--shooting-duration': shootingStar.duration,
                                        '--shooting-delay': shootingStar.delay,
                                        '--shooting-distance': shootingStar.distance
                                    } as React.CSSProperties}
                                ></div>
                            )) :
                            // 后备方案：使用固定配置
                            shootingStarsConfig.map((shootingStar, index) => (
                                <div
                                    key={`fallback-shooting-${index}`}
                                    className="shooting-star"
                                    style={{
                                        top: shootingStar.top,
                                        left: shootingStar.left,
                                        width: shootingStar.width,
                                        '--shooting-duration': shootingStar.duration,
                                        '--shooting-delay': shootingStar.delay,
                                        '--shooting-distance': shootingStar.distance
                                    } as React.CSSProperties}
                                ></div>
                            ))}
                        </div>
                    )}

                    {/* Enhanced gradient overlay for better visual depth */}
                    <div className="absolute inset-0 bg-gradient-to-b from-transparent via-transparent to-background-dark/20 pointer-events-none"></div>
                </div>

                {/* Hero Content Container - 优化布局结构 */}
                <div className="container mx-auto px-4 sm:px-6 relative z-10 flex items-center justify-center min-h-screen">
                    <div className="w-full max-w-6xl mx-auto text-center hero-content">
                        {/* Brand Logo and Domain - 优化移动端间距 */}
                        <div className="flex items-center justify-center space-x-3 sm:space-x-4 md:space-x-6 mb-6 sm:mb-8 md:mb-12">
                            <div className="relative w-14 h-14 sm:w-16 sm:h-16 md:w-20 md:h-20 lg:w-24 lg:h-24 flex-shrink-0">
                                <Image
                                    src="/logo.webp"
                                    alt="a3i.ai Logo"
                                    fill
                                    className="object-contain"
                                    priority
                                    sizes="(max-width: 640px) 56px, (max-width: 768px) 64px, (max-width: 1024px) 80px, 96px"
                                />
                            </div>
                            <div className="flex flex-col items-start">
                                <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-display font-bold text-gradient tracking-tighter leading-none">
                                    a3i.ai
                                </h1>
                                <p className="text-xs sm:text-sm md:text-base lg:text-lg text-text-muted font-title tracking-wide mt-1 opacity-80">
                                    {t('hero.tagline')}
                                </p>
                            </div>
                        </div>

                        {/* Main Slogan - 优化响应式文字大小和间距 */}
                        <div className="mb-6 sm:mb-8 md:mb-12 px-2">
                            <h2 className="font-display text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl 2xl:text-7xl font-bold mb-4 sm:mb-6 md:mb-8 tracking-tighter text-center leading-tight">
                                <span className="block sm:block md:inline">
                                    <span className="hero-title">{t('hero.title')}</span>
                                    <span className="hidden md:inline"> </span>
                                </span>
                                <span className="block sm:block md:inline hero-subtitle mt-1 sm:mt-2 md:mt-0">{t('hero.subtitle')}</span>
                            </h2>
                        </div>

                        {/* Supporting Description - 优化移动端字体大小和行距 */}
                        <p className="font-title text-sm sm:text-base md:text-lg lg:text-xl xl:text-2xl text-text-light mb-8 sm:mb-10 md:mb-12 max-w-4xl xl:max-w-5xl mx-auto tracking-wide leading-relaxed opacity-90 px-2">
                            {t('hero.description')}
                        </p>

                        {/* Prompt Input - 重新设计避免重叠，优化响应式 */}
                        <div className="relative max-w-3xl lg:max-w-4xl mx-auto mb-12 sm:mb-16 md:mb-20 px-2">
                            {/* 移动端：垂直布局 */}
                            <div className="flex flex-col gap-4 sm:hidden">
                                <input
                                    type="text"
                                    value={prompt}
                                    onChange={(e) => setPrompt(e.target.value)}
                                    onKeyPress={handleKeyPress}
                                    placeholder={t('hero.promptPlaceholder')}
                                    className="w-full p-4 rounded-lg bg-dark-card/90 border border-primary/20 text-text-light focus:outline-none focus:ring-2 focus:ring-primary/40 focus:border-primary/30 font-mono text-sm transition-all duration-300 placeholder-text-muted/50 shadow-lg shadow-background-dark/30 min-h-[56px]"
                                />
                                <button
                                    onClick={handleGenerate}
                                    disabled={!prompt.trim()}
                                    className="w-full bg-generate-button hover:brightness-110 active:brightness-90 transition-all duration-300 px-6 py-4 rounded-lg text-text-light font-title font-medium button-text shadow-xl shadow-primary/30 hover:shadow-primary/50 text-sm disabled:opacity-50 disabled:cursor-not-allowed min-h-[56px] flex items-center justify-center"
                                >
                                    {t('hero.generateButton')}
                                </button>
                            </div>

                            {/* 桌面端：水平布局，使用 Grid 确保不重叠 */}
                            <div className="hidden sm:grid sm:grid-cols-[1fr_auto] sm:gap-0 sm:bg-dark-card/90 sm:rounded-xl sm:border sm:border-primary/20 sm:shadow-lg sm:shadow-background-dark/30 sm:overflow-hidden focus-within:ring-2 focus-within:ring-primary/40 focus-within:border-primary/30 transition-all duration-300">
                                <input
                                    type="text"
                                    value={prompt}
                                    onChange={(e) => setPrompt(e.target.value)}
                                    onKeyPress={handleKeyPress}
                                    placeholder={t('hero.promptPlaceholder')}
                                    className="w-full p-4 sm:p-5 md:p-6 bg-transparent border-0 text-text-light focus:outline-none focus:ring-0 font-mono text-sm sm:text-base md:text-lg transition-all duration-300 placeholder-text-muted/50 min-w-0"
                                />
                                <button
                                    onClick={handleGenerate}
                                    disabled={!prompt.trim()}
                                    className="bg-generate-button hover:brightness-110 active:brightness-90 transition-all duration-300 px-6 sm:px-8 md:px-10 py-4 sm:py-5 md:py-6 text-text-light font-title font-medium button-text shadow-xl shadow-primary/30 hover:shadow-primary/50 text-sm sm:text-base md:text-lg disabled:opacity-50 disabled:cursor-not-allowed whitespace-nowrap flex-shrink-0"
                                >
                                    {t('hero.generateButton')}
                                </button>
                            </div>
                        </div>

                        {/* Scroll indicator for better UX - 修复位置协调问题 */}
                        <div className="absolute bottom-6 sm:bottom-8 left-1/2 -translate-x-1/2 animate-bounce hidden lg:block">
                            <div className="w-6 h-10 border-2 border-primary/40 rounded-full flex justify-center items-start pt-2">
                                <div className="w-1 h-3 bg-primary rounded-full animate-pulse"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* Tools Section */}
            <section className="py-12 sm:py-16 bg-dark">
                <div className="container mx-auto px-4">
                    <div className="text-center mb-8 sm:mb-12">
                        <h2 className="font-display text-2xl sm:text-3xl md:text-4xl font-bold mb-3 sm:mb-4 text-gradient section-title">
                            {t('tools.title')}
                        </h2>
                        <p className="text-text-muted text-base sm:text-lg max-w-3xl mx-auto font-title tracking-wide leading-relaxed">
                            {t('tools.subtitle')}
                        </p>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6 sm:gap-8 mb-12">
                        {tools.map(({ key }) => {
                            // Map tool keys to their respective routes
                            const getToolRoute = (toolKey: string): Pathnames => {
                                switch (toolKey) {
                                    case 'imageGenerator':
                                        return '/image-generator' as Pathnames;
                                    case 'avatarCreator':
                                        return '/avatar-creator' as Pathnames;
                                    case 'imageToText':
                                        return '/image-to-text' as Pathnames;
                                    default:
                                        return '/image-generator' as Pathnames;
                                }
                            };

                            const IconComponent = getIconComponent(key);

                            return (
                                <div key={key} className="bg-dark-card rounded-xl p-5 sm:p-6 hover-card border border-primary/10 transition-all duration-300 hover:border-primary/30">
                                    <div className="flex items-center gap-3 mb-3">
                                        <div className="flex items-center justify-center w-10 h-10 sm:w-12 sm:h-12 rounded-lg bg-gradient-to-br from-primary/20 to-secondary/20 border border-primary/30">
                                            <IconComponent className="w-5 h-5 sm:w-6 sm:h-6 text-primary" />
                                        </div>
                                        <h3 className="font-title text-lg sm:text-xl font-semibold text-text-light card-title">
                                            {t(`tools.items.${key}.title`)}
                                        </h3>
                                    </div>
                                    <p className="text-text-muted text-sm sm:text-base mb-4 sm:mb-6 leading-relaxed">
                                        {t(`tools.items.${key}.description`)}
                                    </p>
                                    <Link href={getToolRoute(key)} className="inline-block text-primary hover:text-primary-dark transition-colors font-title button-text text-sm sm:text-base group">
                                        {t('tools.tryButton')}
                                        <span className="inline-block ml-1 transition-transform group-hover:translate-x-1">→</span>
                                    </Link>
                                </div>
                            );
                        })}
                    </div>
                </div>
            </section>

            {/* Gallery Section */}
            <section className="py-12 sm:py-16 bg-dark-lighter">
                <div className="container mx-auto px-4">
                    <div className="text-center mb-8 sm:mb-12">
                        <h2 className="font-display text-2xl sm:text-3xl md:text-4xl font-bold mb-3 sm:mb-4 text-gradient-secondary section-title">
                            {t('gallery.title')}
                        </h2>
                        <p className="text-text-muted text-base sm:text-lg max-w-2xl mx-auto font-title tracking-wide leading-relaxed">
                            {t('gallery.description')}
                        </p>
                    </div>

                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
                        {[1, 2, 3].map((item) => {
                            // Map gallery items to their corresponding images
                            const getImageSrc = (itemNumber: number): string => {
                                switch (itemNumber) {
                                    case 1:
                                        return '/image/futuristic-cyberpunk-cityscape-at-sunset-ai-generated.webp';
                                    case 2:
                                        return '/image/neon-style-ai-generated-female-portrait-digital-art.webp';
                                    case 3:
                                        return '/image/futuristic-humanoid-robot-concept-art-glowing-armor.webp';
                                    default:
                                        return '/image/futuristic-cyberpunk-cityscape-at-sunset-ai-generated.webp';
                                }
                            };

                            return (
                                <div key={item} className="overflow-hidden rounded-xl bg-dark-card hover-card transition-all duration-300 hover:scale-105">
                                    <div className="aspect-w-16 aspect-h-9 relative h-36 sm:h-48">
                                        <Image
                                            src={getImageSrc(item)}
                                            alt={t(`gallery.items.${item}.title`)}
                                            fill
                                            className="object-cover transition-transform duration-300 hover:scale-110"
                                            sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw"
                                        />
                                        <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
                                        <div className="absolute bottom-2 left-2 right-2">
                                            <div className="text-white text-sm font-display font-semibold">
                                                {t(`gallery.items.${item}.title`)}
                                            </div>
                                        </div>
                                    </div>
                                    <div className="p-3 sm:p-4">
                                        <h3 className="font-title text-base sm:text-lg font-semibold text-text-light mb-1 sm:mb-2 card-title">
                                            {t(`gallery.items.${item}.title`)}
                                        </h3>
                                        <p className="text-text-muted text-xs sm:text-sm font-sans">
                                            {t(`gallery.items.${item}.description`)}
                                        </p>
                                    </div>
                                </div>
                            );
                        })}
                    </div>

                    <div className="mt-8 sm:mt-12 text-center">
                        <Link href={"/" as Pathnames} className="inline-block px-5 sm:px-6 py-2 sm:py-3 rounded-lg bg-gradient-secondary text-text-light hover:opacity-90 transition-opacity font-title button-text text-sm sm:text-base group">
                            {t('gallery.exploreButton')}
                            <span className="inline-block ml-2 transition-transform group-hover:translate-x-1">→</span>
                        </Link>
                    </div>
                </div>
            </section>

            {/* FAQ Section */}
            <section className="py-16 sm:py-20 bg-dark">
                <div className="container mx-auto px-4">
                    <div className="text-center mb-12 sm:mb-16">
                        <h2 className="font-display text-2xl sm:text-3xl md:text-4xl font-bold mb-3 sm:mb-4 text-gradient section-title">
                            {t('faq.title')}
                        </h2>
                        <p className="text-text-muted text-base sm:text-lg max-w-3xl mx-auto font-title tracking-wide leading-relaxed">
                            {t('faq.subtitle')}
                        </p>
                    </div>

                    <div className="max-w-4xl mx-auto">
                        <div className="grid gap-4 sm:gap-6">
                            {[1, 2, 3, 4, 5, 6].map((item) => (
                                <details key={item} className="group bg-dark-card border border-primary/10 rounded-xl overflow-hidden transition-all duration-300 hover:border-primary/30">
                                    <summary className="flex items-center justify-between p-4 sm:p-6 cursor-pointer list-none">
                                        <h3 className="font-title text-base sm:text-lg font-semibold text-text-light pr-4 card-title group-open:text-primary transition-colors">
                                            {t(`faq.items.${item}.question`)}
                                        </h3>
                                        <div className="flex-shrink-0 w-6 h-6 flex items-center justify-center">
                                            <svg
                                                className="w-4 h-4 text-primary transition-transform group-open:rotate-180"
                                                fill="none"
                                                stroke="currentColor"
                                                viewBox="0 0 24 24"
                                            >
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                                            </svg>
                                        </div>
                                    </summary>
                                    <div className="faq-content">
                                        <div className="p-4 sm:p-6 pt-0">
                                            <p className="text-text-muted text-sm sm:text-base leading-relaxed font-sans">
                                                {t(`faq.items.${item}.answer`)}
                                            </p>
                                        </div>
                                    </div>
                                </details>
                            ))}
                        </div>

                        {/* FAQ CTA */}
                        <div className="text-center mt-12 sm:mt-16">
                            <div className="bg-dark-card/50 backdrop-blur-sm border border-primary/20 rounded-2xl p-6 sm:p-8">
                                <h3 className="font-display text-xl sm:text-2xl font-bold text-gradient mb-3 sm:mb-4">
                                    {t('faq.cta.title')}
                                </h3>
                                <p className="text-text-muted text-sm sm:text-base mb-6 sm:mb-8 font-title leading-relaxed">
                                    {t('faq.cta.description')}
                                </p>
                                <Link
                                    href={"/" as Pathnames}
                                    className="inline-block px-6 sm:px-8 py-3 sm:py-4 bg-generate-button hover:opacity-95 active:opacity-100 transition-all duration-300 rounded-lg text-text-light font-title font-medium button-text shadow-xl shadow-primary/30 hover:shadow-primary/50 text-sm sm:text-base group"
                                >
                                    {t('faq.cta.button')}
                                    <span className="inline-block ml-2 transition-transform group-hover:translate-x-1">→</span>
                                </Link>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </>
    );
}
