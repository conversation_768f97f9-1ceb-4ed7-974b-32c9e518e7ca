'use client'

import { useState } from 'react'
import { useTranslations } from 'next-intl'
import { useAuth } from '@/hooks/useAuth'
import { Check, Star, Zap, Crown } from 'lucide-react'
import { motion } from 'framer-motion'

interface PricingPlan {
  id: string
  name: string
  description: string
  credits: string
  monthlyPrice: string
  yearlyPrice: string
  features: string[]
  popular: boolean
  icon: React.ReactNode
}

export default function PricingContent() {
  const t = useTranslations('pages.pricing.content')
  const { isAuthenticated, signIn } = useAuth()
  const [isYearly, setIsYearly] = useState(false)

  const plans: PricingPlan[] = [
    {
      id: 'starter',
      name: t('plans.starter.name'),
      description: t('plans.starter.description'),
      credits: t('plans.starter.credits'),
      monthlyPrice: t('plans.starter.price.monthly'),
      yearlyPrice: t('plans.starter.price.yearly'),
      features: [
        t('plans.starter.features.0'),
        t('plans.starter.features.1'),
        t('plans.starter.features.2'),
        t('plans.starter.features.3'),
      ],
      popular: false,
      icon: <Zap className="w-6 h-6" />
    },
    {
      id: 'pro',
      name: t('plans.pro.name'),
      description: t('plans.pro.description'),
      credits: t('plans.pro.credits'),
      monthlyPrice: t('plans.pro.price.monthly'),
      yearlyPrice: t('plans.pro.price.yearly'),
      features: [
        t('plans.pro.features.0'),
        t('plans.pro.features.1'),
        t('plans.pro.features.2'),
        t('plans.pro.features.3'),
        t('plans.pro.features.4'),
      ],
      popular: true,
      icon: <Star className="w-6 h-6" />
    },
    {
      id: 'premium',
      name: t('plans.premium.name'),
      description: t('plans.premium.description'),
      credits: t('plans.premium.credits'),
      monthlyPrice: t('plans.premium.price.monthly'),
      yearlyPrice: t('plans.premium.price.yearly'),
      features: [
        t('plans.premium.features.0'),
        t('plans.premium.features.1'),
        t('plans.premium.features.2'),
        t('plans.premium.features.3'),
        t('plans.premium.features.4'),
        t('plans.premium.features.5'),
      ],
      popular: false,
      icon: <Crown className="w-6 h-6" />
    }
  ]

  const handleSelectPlan = async (planId: string) => {
    if (!isAuthenticated) {
      signIn('google')
      return
    }
    
    // TODO: Implement Stripe checkout
    console.log('Selected plan:', planId, 'Billing:', isYearly ? 'yearly' : 'monthly')
  }

  return (
    <div className="min-h-screen bg-background-dark pt-20">
      <div className="container mx-auto px-4 py-12">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-text-light mb-4">
            {t('title')}
          </h1>
          <p className="text-xl text-text-muted max-w-2xl mx-auto">
            {t('subtitle')}
          </p>
        </div>

        {/* Billing Toggle */}
        <div className="flex items-center justify-center mb-12">
          <div className="bg-dark-card rounded-lg p-1 border border-primary/20">
            <div className="flex items-center">
              <button
                onClick={() => setIsYearly(false)}
                className={`px-6 py-2 rounded-md transition-all font-medium ${
                  !isYearly 
                    ? 'bg-primary text-white' 
                    : 'text-text-muted hover:text-text-light'
                }`}
              >
                {t('billingToggle.monthly')}
              </button>
              <button
                onClick={() => setIsYearly(true)}
                className={`px-6 py-2 rounded-md transition-all font-medium relative ${
                  isYearly 
                    ? 'bg-primary text-white' 
                    : 'text-text-muted hover:text-text-light'
                }`}
              >
                {t('billingToggle.yearly')}
                <span className="absolute -top-2 -right-2 bg-accent text-dark text-xs px-2 py-1 rounded-full font-bold">
                  {t('billingToggle.saveText')}
                </span>
              </button>
            </div>
          </div>
        </div>

        {/* Pricing Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
          {plans.map((plan, index) => (
            <motion.div
              key={plan.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              className={`relative bg-dark-card rounded-xl p-8 border transition-all duration-300 hover:scale-105 ${
                plan.popular 
                  ? 'border-primary shadow-lg shadow-primary/20' 
                  : 'border-primary/20 hover:border-primary/40'
              }`}
            >
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <span className="bg-primary text-white px-4 py-1 rounded-full text-sm font-medium">
                    Most Popular
                  </span>
                </div>
              )}

              <div className="text-center mb-6">
                <div className={`inline-flex items-center justify-center w-12 h-12 rounded-lg mb-4 ${
                  plan.popular ? 'bg-primary/20 text-primary' : 'bg-accent/20 text-accent'
                }`}>
                  {plan.icon}
                </div>
                <h3 className="text-2xl font-bold text-text-light mb-2">{plan.name}</h3>
                <p className="text-text-muted">{plan.description}</p>
              </div>

              <div className="text-center mb-6">
                <div className="text-4xl font-bold text-text-light mb-2">
                  {isYearly ? plan.yearlyPrice : plan.monthlyPrice}
                </div>
                <div className="text-text-muted">
                  {plan.credits}
                </div>
              </div>

              <ul className="space-y-3 mb-8">
                {plan.features.map((feature, featureIndex) => (
                  <li key={featureIndex} className="flex items-center gap-3">
                    <Check className="w-5 h-5 text-green-400 flex-shrink-0" />
                    <span className="text-text-light">{feature}</span>
                  </li>
                ))}
              </ul>

              <button
                onClick={() => handleSelectPlan(plan.id)}
                className={`w-full py-3 px-6 rounded-lg font-medium transition-all ${
                  plan.popular
                    ? 'bg-primary hover:bg-primary-dark text-white'
                    : 'bg-dark-lighter hover:bg-primary/20 text-text-light border border-primary/20 hover:border-primary'
                }`}
              >
                {isAuthenticated ? t('buttons.selectPlan') : t('buttons.getStarted')}
              </button>
            </motion.div>
          ))}
        </div>

        {/* Features Section */}
        <div className="text-center mb-16">
          <h2 className="text-3xl font-bold text-text-light mb-8">{t('features.title')}</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-4xl mx-auto">
            {[0, 1, 2, 3, 4].map((index) => (
              <div key={index} className="flex items-center gap-3 text-text-light">
                <Check className="w-5 h-5 text-green-400 flex-shrink-0" />
                <span>{t(`features.list.${index}`)}</span>
              </div>
            ))}
          </div>
        </div>

        {/* FAQ Section */}
        <div className="max-w-3xl mx-auto">
          <h2 className="text-3xl font-bold text-text-light text-center mb-8">{t('faq.title')}</h2>
          <div className="space-y-6">
            {[1, 2, 3, 4].map((index) => (
              <div key={index} className="bg-dark-card rounded-lg p-6 border border-primary/20">
                <h3 className="text-lg font-semibold text-text-light mb-3">
                  {t(`faq.items.${index}.question`)}
                </h3>
                <p className="text-text-muted">
                  {t(`faq.items.${index}.answer`)}
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}
