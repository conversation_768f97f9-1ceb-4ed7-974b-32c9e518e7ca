'use client';

import { useState, useRef } from 'react';
import { useTranslations } from 'next-intl';
import { motion } from 'framer-motion';
import { Upload, FileText, Download, RotateCcw, Eye, Zap, Globe, Shield } from 'lucide-react';
import Image from 'next/image';

interface AnalysisResult {
    success: boolean;
    description: string;
    prompt: string;
    language: string;
    fileName: string;
    fileSize: number;
    mimeType: string;
}

export default function ImageToTextContent() {
    const [selectedImage, setSelectedImage] = useState<File | null>(null);
    const [imagePreview, setImagePreview] = useState<string | null>(null);
    const [isAnalyzing, setIsAnalyzing] = useState(false);
    const [result, setResult] = useState<AnalysisResult | null>(null);
    const [error, setError] = useState<string>('');
    const [customPrompt, setCustomPrompt] = useState('');
    const [language, setLanguage] = useState('en');
    const fileInputRef = useRef<HTMLInputElement>(null);
    const t = useTranslations('pages.imageToText.content');

    const handleImageSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (file) {
            setSelectedImage(file);
            setError('');
            setResult(null);
            
            // Create preview
            const reader = new FileReader();
            reader.onload = (e) => {
                setImagePreview(e.target?.result as string);
            };
            reader.readAsDataURL(file);
        }
    };

    const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
        event.preventDefault();
        const file = event.dataTransfer.files[0];
        if (file && file.type.startsWith('image/')) {
            setSelectedImage(file);
            setError('');
            setResult(null);
            
            const reader = new FileReader();
            reader.onload = (e) => {
                setImagePreview(e.target?.result as string);
            };
            reader.readAsDataURL(file);
        }
    };

    const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
        event.preventDefault();
    };

    const handleAnalyze = async () => {
        if (!selectedImage) return;

        setIsAnalyzing(true);
        setError('');

        try {
            const formData = new FormData();
            formData.append('image', selectedImage);
            formData.append('prompt', customPrompt || 'Describe this image in detail.');
            formData.append('language', language);

            const response = await fetch('/api/image-to-text', {
                method: 'POST',
                body: formData,
            });

            const data = await response.json();

            if (!data.success) {
                throw new Error(data.error || 'Analysis failed');
            }

            setResult(data);
        } catch (err) {
            console.error('Error analyzing image:', err);
            if (err instanceof Error) {
                setError(err.message);
            } else {
                setError('An unexpected error occurred. Please try again.');
            }
        } finally {
            setIsAnalyzing(false);
        }
    };

    const handleDownload = () => {
        if (result) {
            const content = `Image Analysis Result\n\nFile: ${result.fileName}\nPrompt: ${result.prompt}\nLanguage: ${result.language}\n\nDescription:\n${result.description}`;
            const blob = new Blob([content], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `image-analysis-${Date.now()}.txt`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);
        }
    };

    const handleReset = () => {
        setSelectedImage(null);
        setImagePreview(null);
        setResult(null);
        setError('');
        setCustomPrompt('');
        if (fileInputRef.current) {
            fileInputRef.current.value = '';
        }
    };

    return (
        <div className="container mx-auto px-4 py-8 sm:py-12">
            {/* Hero Section */}
            <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                className="text-center mb-12"
            >
                <h1 className="text-4xl sm:text-5xl lg:text-6xl font-display font-bold text-gradient mb-6">
                    {t('hero.title')}
                </h1>
                <p className="text-xl sm:text-2xl text-primary font-title mb-4">
                    {t('hero.subtitle')}
                </p>
                <p className="text-lg text-text-light max-w-3xl mx-auto leading-relaxed">
                    {t('hero.description')}
                </p>
            </motion.div>

            {/* Main Content */}
            <div className="max-w-6xl mx-auto">
                <div className="grid lg:grid-cols-2 gap-8 mb-12">
                    {/* Upload Section */}
                    <motion.div
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.6, delay: 0.2 }}
                        className="space-y-6"
                    >
                        <h2 className="text-2xl font-title font-bold text-gradient mb-4">
                            {t('upload.title')}
                        </h2>

                        {/* File Upload Area */}
                        <div
                            className="border-2 border-dashed border-primary/30 rounded-xl p-8 text-center hover:border-primary/50 transition-colors cursor-pointer"
                            onDrop={handleDrop}
                            onDragOver={handleDragOver}
                            onClick={() => fileInputRef.current?.click()}
                        >
                            <Upload className="w-12 h-12 text-primary mx-auto mb-4" />
                            <p className="text-text-light mb-2">{t('upload.dragDrop')}</p>
                            <p className="text-text-muted text-sm">{t('upload.supportedFormats')}</p>
                            <input
                                ref={fileInputRef}
                                type="file"
                                accept="image/*"
                                onChange={handleImageSelect}
                                className="hidden"
                            />
                        </div>

                        {/* Image Preview */}
                        {imagePreview && (
                            <div className="relative rounded-xl overflow-hidden border border-primary/20">
                                <Image
                                    src={imagePreview}
                                    alt="Selected image"
                                    width={500}
                                    height={300}
                                    className="w-full h-auto object-cover"
                                />
                            </div>
                        )}

                        {/* Options */}
                        <div className="space-y-4">
                            <div>
                                <label className="block text-text-light font-title mb-2">
                                    {t('options.customPrompt')}
                                </label>
                                <textarea
                                    value={customPrompt}
                                    onChange={(e) => setCustomPrompt(e.target.value)}
                                    placeholder={t('options.promptPlaceholder')}
                                    className="w-full px-4 py-3 bg-dark-card border border-primary/20 rounded-lg text-text-light placeholder-text-muted focus:outline-none focus:border-primary/50 resize-none"
                                    rows={3}
                                />
                            </div>

                            <div>
                                <label className="block text-text-light font-title mb-2">
                                    {t('options.language')}
                                </label>
                                <select
                                    value={language}
                                    onChange={(e) => setLanguage(e.target.value)}
                                    className="w-full px-4 py-3 bg-dark-card border border-primary/20 rounded-lg text-text-light focus:outline-none focus:border-primary/50 select-dropdown"
                                >
                                    <option value="en">{t('options.languages.en')}</option>
                                    <option value="zh">{t('options.languages.zh')}</option>
                                </select>
                            </div>
                        </div>

                        {/* Action Buttons */}
                        <div className="flex gap-4">
                            <button
                                onClick={handleAnalyze}
                                disabled={!selectedImage || isAnalyzing}
                                className="flex-1 btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                {isAnalyzing ? (
                                    <>
                                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                                        {t('buttons.analyzing')}
                                    </>
                                ) : (
                                    <>
                                        <Eye className="w-4 h-4 mr-2" />
                                        {t('buttons.analyze')}
                                    </>
                                )}
                            </button>
                            
                            <button
                                onClick={handleReset}
                                className="px-6 py-3 bg-dark-card border border-primary/20 text-text-light rounded-lg hover:border-primary/40 transition-colors"
                            >
                                <RotateCcw className="w-4 h-4" />
                            </button>
                        </div>
                    </motion.div>

                    {/* Results Section */}
                    <motion.div
                        initial={{ opacity: 0, x: 20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.6, delay: 0.4 }}
                        className="space-y-6"
                    >
                        <h2 className="text-2xl font-title font-bold text-gradient mb-4">
                            {t('results.title')}
                        </h2>

                        {error && (
                            <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4">
                                <p className="text-red-400">{error}</p>
                            </div>
                        )}

                        {result && (
                            <div className="bg-dark-card/50 rounded-xl p-6 border border-primary/20">
                                <div className="flex justify-between items-start mb-4">
                                    <h3 className="text-lg font-title font-semibold text-text-light">
                                        {t('results.description')}
                                    </h3>
                                    <button
                                        onClick={handleDownload}
                                        className="p-2 text-primary hover:bg-primary/10 rounded-lg transition-colors"
                                        title={t('buttons.download')}
                                    >
                                        <Download className="w-4 h-4" />
                                    </button>
                                </div>
                                
                                <div className="prose prose-invert max-w-none">
                                    <p className="text-text-light leading-relaxed whitespace-pre-wrap">
                                        {result.description}
                                    </p>
                                </div>

                                <div className="mt-4 pt-4 border-t border-primary/10">
                                    <div className="grid grid-cols-2 gap-4 text-sm text-text-muted">
                                        <div>
                                            <span className="font-semibold">{t('results.fileName')}:</span> {result.fileName}
                                        </div>
                                        <div>
                                            <span className="font-semibold">{t('results.fileSize')}:</span> {(result.fileSize / 1024).toFixed(1)} KB
                                        </div>
                                    </div>
                                </div>
                            </div>
                        )}

                        {!result && !error && (
                            <div className="bg-dark-card/30 rounded-xl p-8 border border-primary/10 text-center">
                                <FileText className="w-12 h-12 text-primary/50 mx-auto mb-4" />
                                <p className="text-text-muted">{t('results.placeholder')}</p>
                            </div>
                        )}
                    </motion.div>
                </div>
            </div>

            {/* Features Section */}
            <section className="py-12 sm:py-16 bg-dark-card/20">
                <div className="container mx-auto px-4">
                    <div className="text-center mb-12">
                        <h2 className="font-display text-2xl sm:text-3xl md:text-4xl font-bold mb-4 text-gradient">
                            {t('features.title')}
                        </h2>
                        <p className="text-text-muted text-lg max-w-2xl mx-auto">
                            {t('features.subtitle')}
                        </p>
                    </div>

                    <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
                        {['accuracy', 'speed', 'multilingual', 'privacy'].map((feature, index) => (
                            <motion.div
                                key={feature}
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.6, delay: index * 0.1 }}
                                className="bg-dark-card/50 rounded-xl p-6 border border-primary/20 hover:border-primary/40 transition-all duration-300"
                            >
                                <div className="flex items-center justify-center w-12 h-12 rounded-lg bg-gradient-to-br from-primary/20 to-secondary/20 border border-primary/30 mb-4">
                                    {feature === 'accuracy' ? <Eye className="w-6 h-6 text-primary" /> :
                                     feature === 'speed' ? <Zap className="w-6 h-6 text-primary" /> :
                                     feature === 'multilingual' ? <Globe className="w-6 h-6 text-primary" /> :
                                     <Shield className="w-6 h-6 text-primary" />}
                                </div>
                                <h3 className="text-xl font-title font-bold text-gradient mb-3">
                                    {t(`features.items.${feature}.title`)}
                                </h3>
                                <p className="text-text-light leading-relaxed">
                                    {t(`features.items.${feature}.description`)}
                                </p>
                            </motion.div>
                        ))}
                    </div>
                </div>
            </section>

            {/* Use Cases Section */}
            <section className="py-12 sm:py-16">
                <div className="container mx-auto px-4">
                    <div className="text-center mb-12">
                        <h2 className="font-display text-2xl sm:text-3xl md:text-4xl font-bold mb-4 text-gradient">
                            {t('useCases.title')}
                        </h2>
                        <p className="text-text-muted text-lg max-w-2xl mx-auto">
                            {t('useCases.subtitle')}
                        </p>
                    </div>

                    <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                        {['accessibility', 'content', 'research', 'education', 'business', 'social'].map((useCase, index) => (
                            <motion.div
                                key={useCase}
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.6, delay: index * 0.1 }}
                                className="bg-dark-card/50 rounded-xl p-6 border border-primary/20 hover:border-primary/40 transition-all duration-300"
                            >
                                <h4 className="font-title font-semibold text-text-light mb-3 text-lg">
                                    {t(`useCases.items.${useCase}.title`)}
                                </h4>
                                <p className="text-text-muted text-sm leading-relaxed mb-4">
                                    {t(`useCases.items.${useCase}.description`)}
                                </p>
                                <div className="text-xs text-primary font-semibold">
                                    {t(`useCases.items.${useCase}.example`)}
                                </div>
                            </motion.div>
                        ))}
                    </div>
                </div>
            </section>
        </div>
    );
}
