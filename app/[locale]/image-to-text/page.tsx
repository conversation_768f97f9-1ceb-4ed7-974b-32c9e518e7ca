import { Metadata } from 'next';
import { getTranslations, setRequestLocale } from 'next-intl/server';
import { notFound } from 'next/navigation';
import { routing } from '@/i18n/routing';
import ImageToTextContent from './ImageToTextContent';

type Props = {
    params: Promise<{ locale: string }>;
};

export async function generateMetadata({ params }: Props): Promise<Metadata> {
    const { locale } = await params;

    if (!routing.locales.includes(locale as any)) {
        notFound();
    }

    const t = await getTranslations({ locale, namespace: 'pages.imageToText.metadata' });

    return {
        title: t('title'),
        description: t('description'),
        keywords: 'AI image to text, image description, Google Gemini, image captioning, OCR, image analysis, visual AI, text extraction, image recognition, AI vision',
        openGraph: {
            title: t('title'),
            description: t('description'),
            url: `https://a3i.ai/${locale}/image-to-text`,
            siteName: 'a3i.ai',
            images: [
                {
                    url: 'https://a3i.ai/hero-background-ai-abstract-blue-purple-gradient.webp',
                    width: 1200,
                    height: 630,
                    alt: 'AI Image to Text Converter',
                },
            ],
            locale: locale,
            type: 'website',
        },
        twitter: {
            card: 'summary_large_image',
            title: t('title'),
            description: t('description'),
            images: ['https://a3i.ai/hero-background-ai-abstract-blue-purple-gradient.webp'],
        },
        alternates: {
            canonical: `https://a3i.ai/${locale}/image-to-text`,
            languages: {
                'en': 'https://a3i.ai/en/image-to-text',
                'zh': 'https://a3i.ai/zh/image-to-text',
            },
        },
    };
}

export default async function ImageToTextPage({ params }: Props) {
    const { locale } = await params;

    if (!routing.locales.includes(locale as any)) {
        notFound();
    }

    setRequestLocale(locale);
    const t = await getTranslations('pages.imageToText.content');

    return (
        <>
            {/* Structured Data */}
            <script
                type="application/ld+json"
                dangerouslySetInnerHTML={{
                    __html: JSON.stringify({
                        "@context": "https://schema.org",
                        "@type": "SoftwareApplication",
                        "name": "AI Image to Text Converter - Powered by Google Gemini",
                        "description": "Advanced AI image analysis and description tool powered by Google's Gemini multimodal AI. Extract text, generate captions, and analyze images with state-of-the-art AI technology.",
                        "url": `https://a3i.ai/${locale}/image-to-text`,
                        "applicationCategory": "UtilityApplication",
                        "operatingSystem": "Web Browser",
                        "offers": {
                            "@type": "Offer",
                            "price": "0",
                            "priceCurrency": "USD"
                        },
                        "creator": {
                            "@type": "Organization",
                            "name": "a3i.ai",
                            "url": "https://a3i.ai"
                        },
                        "provider": {
                            "@type": "Organization",
                            "name": "Google",
                            "url": "https://google.com"
                        },
                        "featureList": [
                            "AI Image Analysis",
                            "Google Gemini Integration",
                            "Automatic Image Captioning",
                            "Text Extraction from Images",
                            "Multilingual Support",
                            "High Accuracy Recognition",
                            "Fast Processing Speed"
                        ],
                        "about": {
                            "@type": "Thing",
                            "name": "Google Gemini",
                            "description": "Google's advanced multimodal AI model that can understand and analyze images, generating detailed descriptions and extracting information"
                        }
                    })
                }}
            />

            <main className="min-h-screen pt-20 sm:pt-24">
                <ImageToTextContent />
            </main>
        </>
    );
}
