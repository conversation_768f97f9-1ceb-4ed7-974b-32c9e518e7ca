import { Metadata } from 'next';
import { getTranslations, setRequestLocale } from 'next-intl/server';
import { notFound } from 'next/navigation';
import { routing } from '@/i18n/routing';
import { Suspense } from 'react';
import ImageGeneratorContent from './ImageGeneratorContent';

type Props = {
    params: Promise<{ locale: string }>;
};

export async function generateMetadata({ params }: Props): Promise<Metadata> {
    const { locale } = await params;

    if (!routing.locales.includes(locale as any)) {
        notFound();
    }

    const t = await getTranslations({ locale, namespace: 'pages.imageGenerator.metadata' });

    return {
        title: t('title'),
        description: t('description'),
        keywords: t('keywords'),
        openGraph: {
            title: t('title'),
            description: t('description'),
            type: 'website',
            locale: locale,
            url: `https://a3i.ai/${locale}/image-generator`,
            siteName: 'a3i.ai',
            images: [
                {
                    url: 'https://a3i.ai/og-image.jpg',
                    width: 1200,
                    height: 630,
                    alt: t('title'),
                },
            ],
        },
        twitter: {
            card: 'summary_large_image',
            title: t('title'),
            description: t('description'),
            images: ['https://a3i.ai/og-image.jpg'],
        },
        alternates: {
            canonical: `https://a3i.ai/${locale}/image-generator`,
            languages: {
                'en': 'https://a3i.ai/en/image-generator',
                'zh': 'https://a3i.ai/zh/image-generator',
            },
        },
    };
}

export default async function ImageGeneratorPage({ params }: Props) {
    const { locale } = await params;

    if (!routing.locales.includes(locale as any)) {
        notFound();
    }

    setRequestLocale(locale);
    const t = await getTranslations('pages.imageGenerator.content');

    // Structured data for SEO
    const structuredData = {
        "@context": "https://schema.org",
        "@type": "WebApplication",
        "name": t('hero.title'),
        "description": t('hero.description'),
        "url": `https://a3i.ai/${locale}/image-generator`,
        "applicationCategory": "DesignApplication",
        "operatingSystem": "Web Browser",
        "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD"
        },
        "creator": {
            "@type": "Organization",
            "name": "a3i.ai",
            "url": "https://a3i.ai"
        }
    };

    return (
        <>
            <script
                type="application/ld+json"
                dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
            />
            <Suspense fallback={
                <div className="min-h-screen bg-dark pt-20 flex items-center justify-center">
                    <div className="text-center">
                        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
                        <p className="text-text-light">Loading...</p>
                    </div>
                </div>
            }>
                <ImageGeneratorContent />
            </Suspense>
        </>
    );
}
