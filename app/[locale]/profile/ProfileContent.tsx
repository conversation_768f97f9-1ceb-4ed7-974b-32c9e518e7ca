'use client'

import { useEffect, useState } from 'react'
import { useTranslations } from 'next-intl'
import { useAuth } from '@/hooks/useAuth'
import { useCredits } from '@/hooks/useCredits'
import { useRouter } from 'next/navigation'
import Image from 'next/image'
import { User, Coins, Calendar, Activity, CreditCard, Settings } from 'lucide-react'
import { Link } from '@/i18n/routing'

interface CreditTransaction {
  id: string
  type: string
  amount: number
  description: string | null
  createdAt: string
}

export default function ProfileContent() {
  const t = useTranslations('pages.profile.content')
  const { user, isAuthenticated, isLoading } = useAuth()
  const { credits, loading: creditsLoading, refresh: refreshCredits } = useCredits()
  const router = useRouter()
  const [transactions, setTransactions] = useState<CreditTransaction[]>([])
  const [loadingTransactions, setLoadingTransactions] = useState(false)

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/auth/signin')
    }
  }, [isAuthenticated, isLoading, router])

  useEffect(() => {
    if (isAuthenticated && user) {
      fetchTransactions()
    }
  }, [isAuthenticated, user])

  const fetchTransactions = async () => {
    setLoadingTransactions(true)
    try {
      const response = await fetch('/api/credits/transactions')
      if (response.ok) {
        const data = await response.json()
        setTransactions(data.transactions || [])
      }
    } catch (error) {
      console.error('Error fetching transactions:', error)
    } finally {
      setLoadingTransactions(false)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString()
  }

  const getTransactionTypeText = (type: string) => {
    return t(`sections.credits.transactionTypes.${type}`)
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-background-dark flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    )
  }

  if (!isAuthenticated || !user) {
    return null
  }

  return (
    <div className="min-h-screen bg-background-dark pt-20">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-text-light mb-2">{t('title')}</h1>
          <p className="text-text-muted">{t('subtitle')}</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column - Profile Info */}
          <div className="lg:col-span-1">
            {/* Profile Card */}
            <div className="bg-dark-card rounded-lg p-6 border border-primary/20 mb-6">
              <div className="flex items-center space-x-4 mb-6">
                {user.image ? (
                  <Image
                    src={user.image}
                    alt={user.name || 'User'}
                    width={80}
                    height={80}
                    className="rounded-full"
                  />
                ) : (
                  <div className="w-20 h-20 rounded-full bg-primary/20 flex items-center justify-center">
                    <User className="w-8 h-8 text-primary" />
                  </div>
                )}
                <div>
                  <h2 className="text-xl font-semibold text-text-light">{user.name}</h2>
                  <p className="text-text-muted">{user.email}</p>
                </div>
              </div>

              {/* Account Overview */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-text-light">{t('sections.overview.title')}</h3>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Calendar className="w-4 h-4 text-text-muted" />
                      <span className="text-text-muted">{t('sections.overview.joinedDate')}</span>
                    </div>
                    <span className="text-text-light">{formatDate(user.createdAt || new Date().toISOString())}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Credits Card */}
            <div className="bg-dark-card rounded-lg p-6 border border-primary/20">
              <h3 className="text-lg font-semibold text-text-light mb-4">{t('sections.credits.title')}</h3>
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-3">
                  <Coins className="w-6 h-6 text-accent" />
                  <div>
                    <p className="text-text-muted text-sm">{t('sections.credits.current')}</p>
                    <p className="text-2xl font-bold text-accent">
                      {creditsLoading ? '...' : credits} {t('sections.credits.credits')}
                    </p>
                  </div>
                </div>
              </div>
              <Link
                href="/pricing"
                className="w-full bg-primary hover:bg-primary-dark text-white py-2 px-4 rounded-lg transition-colors font-medium text-center block"
              >
                {t('sections.credits.getMore')}
              </Link>
            </div>
          </div>

          {/* Right Column - Transactions & History */}
          <div className="lg:col-span-2">
            {/* Recent Transactions */}
            <div className="bg-dark-card rounded-lg p-6 border border-primary/20 mb-6">
              <h3 className="text-lg font-semibold text-text-light mb-4">{t('sections.credits.recentTransactions')}</h3>
              {loadingTransactions ? (
                <div className="flex justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                </div>
              ) : transactions.length > 0 ? (
                <div className="space-y-3">
                  {transactions.slice(0, 10).map((transaction) => (
                    <div key={transaction.id} className="flex items-center justify-between py-3 border-b border-primary/10 last:border-b-0">
                      <div>
                        <p className="text-text-light font-medium">{getTransactionTypeText(transaction.type)}</p>
                        {transaction.description && (
                          <p className="text-text-muted text-sm">{transaction.description}</p>
                        )}
                        <p className="text-text-muted text-xs">{formatDate(transaction.createdAt)}</p>
                      </div>
                      <div className={`font-semibold ${transaction.amount > 0 ? 'text-green-400' : 'text-red-400'}`}>
                        {transaction.amount > 0 ? '+' : ''}{transaction.amount}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-text-muted text-center py-8">{t('sections.credits.noTransactions')}</p>
              )}
            </div>

            {/* Generation History Placeholder */}
            <div className="bg-dark-card rounded-lg p-6 border border-primary/20">
              <h3 className="text-lg font-semibold text-text-light mb-4">{t('sections.history.title')}</h3>
              <p className="text-text-muted text-center py-8">{t('sections.history.noHistory')}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
