import { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';
import { useTranslations } from 'next-intl';
import { getBaseUrl } from '@/utils/metadata';

type Props = {
    params: Promise<{ locale: string }>;
};

export async function generateMetadata({
    params
}: Props): Promise<Metadata> {
    const resolvedParams = await params;
    const locale = resolvedParams.locale;

    const t = await getTranslations('pages.terms.metadata');
    const baseUrl = await getBaseUrl();

    return {
        title: t('title'),
        description: t('description'),
        openGraph: {
            title: t('title'),
            description: t('description'),
            type: 'website',
            url: `${baseUrl}${locale === 'en' ? '' : `/${locale}`}/terms-of-service`,
        }
    };
}

export default function TermsOfService() {
    const t = useTranslations('pages.terms.content');
    const sectionKeys = ['acceptance', 'userContent', 'intellectualProperty', 'disclaimer', 'changes'];

    return (
        <div className="flex flex-col min-h-screen content-container pb-12">
            <div className="container mx-auto px-4 py-8 flex-grow">
                <div className="max-w-4xl mx-auto">
                    {/* Background Elements */}
                    <div className="absolute top-0 left-0 w-full h-64 overflow-hidden -z-10 opacity-30">
                        <div className="absolute -top-20 -right-20 w-64 h-64 bg-secondary/20 rounded-full filter blur-3xl"></div>
                        <div className="absolute top-40 -left-20 w-80 h-80 bg-accent/20 rounded-full filter blur-3xl"></div>
                    </div>

                    <div className="text-center space-y-6 mb-16">
                        <h1 className="font-display text-4xl sm:text-5xl font-bold tracking-tighter hero-subtitle">
                            {t('title')}
                        </h1>
                        <p className="font-title text-lg sm:text-xl text-text-light opacity-90 tracking-wide">
                            {t('description')}
                        </p>
                    </div>

                    <div className="space-y-8">
                        {sectionKeys.map((key) => (
                            <section key={key}
                                className="bg-dark-card rounded-xl p-6 sm:p-8 border border-secondary/10 hover-card">
                                <h2 className="font-title text-xl sm:text-2xl font-semibold mb-4 text-gradient-secondary card-title">
                                    {t(`sections.${key}.title`)}
                                </h2>
                                <div className="space-y-4">
                                    <p className="text-text-light opacity-90 tracking-wide">
                                        {t(`sections.${key}.paragraph1`)}
                                    </p>
                                    <p className="text-text-light opacity-90 tracking-wide">
                                        {t(`sections.${key}.paragraph2`)}
                                    </p>
                                    {key === 'changes' && (
                                        <p className="text-text-light opacity-90 tracking-wide">
                                            {t(`sections.${key}.paragraph3`)}
                                        </p>
                                    )}
                                </div>
                            </section>
                        ))}
                    </div>

                    <p className="text-sm text-text-muted text-center mt-12 font-sans tracking-wide">
                        {t('lastUpdated')}
                    </p>
                </div>
            </div>
        </div>
    );
}