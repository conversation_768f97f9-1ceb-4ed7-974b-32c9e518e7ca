import { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';
import { getBaseUrl } from '@/utils/metadata';
import Script from 'next/script';
import { Suspense } from 'react';
import HomeContent from './HomeContent';

export async function generateMetadata({
    params
}: {
    params: Promise<{ locale: string }>
}): Promise<Metadata> {
    const resolvedParams = await params;
    const locale = resolvedParams.locale;

    const t = await getTranslations('pages.home.metadata');
    const baseUrl = await getBaseUrl();

    return {
        title: t('title'),
        description: t('description'),
        keywords: 'a3i.ai, AI image generator, AI visual creation, create beyond imagination, AI art generator, AI avatar creator, visual AI tools, AI design platform',
        openGraph: {
            title: t('title'),
            description: t('description'),
            type: 'website',
            url: `${baseUrl}${locale === 'en' ? '' : `/${locale}`}`,
            siteName: 'a3i.ai',
            images: [
                {
                    url: '/hero-background-ai-abstract-blue-purple-gradient.webp',
                    width: 1200,
                    height: 630,
                    alt: 'a3i.ai - AI Visual Creation Platform',
                }
            ],
        },
        twitter: {
            card: 'summary_large_image',
            title: t('title'),
            description: t('description'),
            images: ['/hero-background-ai-abstract-blue-purple-gradient.webp'],
        },
        alternates: {
            canonical: `${baseUrl}${locale === 'en' ? '' : `/${locale}`}`,
            languages: {
                'en': `${baseUrl}`,
                'zh': `${baseUrl}/zh`,
            },
        },
        robots: {
            index: true,
            follow: true,
            googleBot: {
                index: true,
                follow: true,
                'max-video-preview': -1,
                'max-image-preview': 'large',
                'max-snippet': -1,
            },
        },
    };
}

export default async function Home() {
    const t = await getTranslations('pages.home.content');
    const baseUrl = await getBaseUrl();

    // AI Tools
    const tools = [
        { key: 'imageGenerator' },
        { key: 'imageToText' },
        { key: 'avatarCreator' },
    ];

    // FAQ结构化数据
    const faqStructuredData = {
        "@context": "https://schema.org",
        "@type": "FAQPage",
        "mainEntity": [1, 2, 3, 4, 5, 6].map(item => ({
            "@type": "Question",
            "name": t(`faq.items.${item}.question`),
            "acceptedAnswer": {
                "@type": "Answer",
                "text": t(`faq.items.${item}.answer`)
            }
        }))
    };

    // 网站结构化数据
    const websiteStructuredData = {
        "@context": "https://schema.org",
        "@type": "WebSite",
        "name": "a3i.ai",
        "alternateName": "a3i - AI Visual Creation Platform",
        "url": baseUrl,
        "description": "a3i.ai is the leading AI visual creation platform. Create beyond imagination with advanced AI tools for images, videos, and avatars.",
        "potentialAction": {
            "@type": "SearchAction",
            "target": {
                "@type": "EntryPoint",
                "urlTemplate": `${baseUrl}/?q={search_term_string}`
            },
            "query-input": "required name=search_term_string"
        }
    };

    // 组织结构化数据
    const organizationStructuredData = {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "a3i.ai",
        "alternateName": "a3i",
        "url": baseUrl,
        "logo": `${baseUrl}/logo.webp`,
        "description": "a3i.ai - Advanced AI visual creation platform. Create beyond imagination with cutting-edge AI technology.",
        "foundingDate": "2024",
        "sameAs": [
            "https://twitter.com/a3i_ai",
            "https://github.com/a3i-ai"
        ],
        "contactPoint": {
            "@type": "ContactPoint",
            "contactType": "customer service",
            "availableLanguage": ["English", "Chinese"]
        }
    };

    return (
        <>
            {/* 结构化数据 */}
            <Script id="faq-structured-data" type="application/ld+json">
                {JSON.stringify(faqStructuredData)}
            </Script>
            <Script id="website-structured-data" type="application/ld+json">
                {JSON.stringify(websiteStructuredData)}
            </Script>
            <Script id="organization-structured-data" type="application/ld+json">
                {JSON.stringify(organizationStructuredData)}
            </Script>

            <Suspense fallback={
                <div className="min-h-screen bg-dark flex items-center justify-center">
                    <div className="text-center">
                        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
                        <p className="text-text-light">Loading...</p>
                    </div>
                </div>
            }>
                <HomeContent tools={tools} />
            </Suspense>
        </>
    );
}