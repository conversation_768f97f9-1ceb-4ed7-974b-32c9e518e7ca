import { Metadata } from 'next';
import { Link, Pathnames } from '@/i18n/routing';
import { getTranslations } from 'next-intl/server';

export async function generateMetadata(): Promise<Metadata> {
    return {
        title: 'Access Denied - a3i.ai',
        description: 'Your IP address is not authorized to access this site.',
        robots: 'noindex, nofollow, noarchive',
    };
}

export default async function BlockedPage() {
    const t = await getTranslations('pages.blocked');

    return (
        <div className="flex flex-col min-h-screen content-container">
            <section className="flex flex-col items-center justify-center py-20 flex-grow">
                <div className="container mx-auto px-4 text-center">
                    <h1 className="font-display text-4xl sm:text-5xl md:text-6xl font-bold mb-6 hero-title tracking-tighter">
                        {t('title') || 'Access Denied'}
                    </h1>

                    <div className="max-w-2xl mx-auto bg-dark-card rounded-xl p-8 border border-primary/10 mb-8">
                        <div className="text-5xl mb-6">🔒</div>
                        <h2 className="font-title text-xl sm:text-2xl font-semibold mb-4 text-gradient">
                            {t('subtitle') || 'IP Restriction Active'}
                        </h2>
                        <p className="text-text-light text-base sm:text-lg mb-6 leading-relaxed">
                            {t('description') ||
                                'This site is currently restricted to authorized IP addresses only. If you believe you should have access, please contact the site administrator with your IP address.'}
                        </p>

                        <div className="bg-dark p-4 rounded-lg inline-block font-mono text-xs sm:text-sm text-text-muted mb-6">
                            {t('ipInfo') || 'Your IP address has been recorded for security purposes.'}
                        </div>

                        <div>
                            <button
                                onClick={() => window.location.reload()}
                                className="inline-block px-5 sm:px-6 py-2 sm:py-3 rounded-lg bg-gradient-primary text-text-light hover:opacity-90 transition-opacity font-title button-text text-sm sm:text-base mr-4"
                            >
                                {t('tryAgainButton') || 'Try Again'}
                            </button>
                        </div>
                    </div>

                    <div className="text-text-muted text-xs sm:text-sm">
                        {t('contactMessage') || 'For any questions, please contact'} <span className="text-primary"><EMAIL></span>
                    </div>
                </div>
            </section>
        </div>
    );
} 