'use client';

import { useState } from 'react';
import { useTranslations } from 'next-intl';
import { motion } from 'framer-motion';
import { Link } from '@/i18n/routing';
import { Star, Zap, Palette, Briefcase, Image as ImageIcon } from 'lucide-react';

interface GeneratedImage {
    success: boolean;
    imageUrl: string;
    dataUrl?: string;
    prompt: string;
    style: string;
    aspectRatio: string;
    dimensions: {
        width: number;
        height: number;
    };
}

export default function ImagenContent() {
    const t = useTranslations('pages.imagen.content');
    const [prompt, setPrompt] = useState('');
    const [style, setStyle] = useState('photorealistic');
    const [aspectRatio, setAspectRatio] = useState('square');
    const [isGenerating, setIsGenerating] = useState(false);
    const [generatedImage, setGeneratedImage] = useState<GeneratedImage | null>(null);
    const [error, setError] = useState('');

    const handleGenerate = async () => {
        if (!prompt.trim()) {
            setError('Please enter a prompt to generate an image');
            return;
        }

        setIsGenerating(true);
        setError('');

        try {
            const response = await fetch('/api/imagen', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    prompt: prompt.trim(),
                    style,
                    aspectRatio,
                }),
            });

            const data = await response.json();

            if (!response.ok) {
                // Handle different types of errors with user-friendly messages
                let errorMessage = 'Failed to generate image. Please try again.';

                if (response.status === 400) {
                    errorMessage = data.error || 'Invalid request. Please check your prompt and try again.';
                } else if (response.status === 401 || response.status === 403) {
                    errorMessage = 'Authentication error. Please refresh the page and try again.';
                } else if (response.status === 429) {
                    errorMessage = 'Too many requests. Please wait a moment and try again.';
                } else if (response.status >= 500) {
                    errorMessage = 'Server error. Please try again in a few moments.';
                } else {
                    errorMessage = data.error || errorMessage;
                }

                throw new Error(errorMessage);
            }

            if (!data.success) {
                throw new Error(data.error || 'Image generation failed');
            }

            setGeneratedImage(data);
        } catch (err) {
            console.error('Error generating image:', err);
            if (err instanceof Error) {
                setError(err.message);
            } else {
                setError('An unexpected error occurred. Please try again.');
            }
        } finally {
            setIsGenerating(false);
        }
    };

    const handleDownload = () => {
        if (generatedImage) {
            // Create a temporary download link
            const link = document.createElement('a');
            // Use dataUrl if available, otherwise use imageUrl
            link.href = generatedImage.dataUrl || generatedImage.imageUrl;
            link.download = `a3i-imagen-${Date.now()}.png`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    };

    const handleNewImage = () => {
        setGeneratedImage(null);
        setError('');
    };

    return (
        <>
            {/* Hero Section */}
            <section className="relative py-16 sm:py-20 lg:py-24">
                <div className="container mx-auto px-4">
                    <div className="max-w-4xl mx-auto text-center space-y-6">
                        <motion.h1 
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.6 }}
                            className="text-4xl sm:text-5xl lg:text-6xl font-display font-bold text-gradient leading-tight"
                        >
                            {t('hero.title')}
                        </motion.h1>
                        <motion.p 
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.6, delay: 0.1 }}
                            className="text-xl sm:text-2xl text-primary font-title"
                        >
                            {t('hero.subtitle')}
                        </motion.p>
                        <motion.p 
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.6, delay: 0.2 }}
                            className="text-lg text-text-light max-w-3xl mx-auto leading-relaxed"
                        >
                            {t('hero.description')}
                        </motion.p>
                    </div>
                </div>
            </section>

            {/* Generator Section */}
            <section className="py-16 sm:py-20 bg-dark-card/30">
                <div className="container mx-auto px-4">
                    <div className="max-w-4xl mx-auto">
                        <div className="text-center mb-12">
                            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-display font-bold text-gradient mb-6">
                                {t('generator.title')}
                            </h2>
                            <p className="text-xl text-primary font-title mb-4">
                                {t('generator.subtitle')}
                            </p>
                            <p className="text-lg text-text-light max-w-2xl mx-auto leading-relaxed">
                                {t('generator.description')}
                            </p>
                        </div>

                        {!generatedImage ? (
                            <motion.div
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.6 }}
                                className="bg-dark-card/50 backdrop-blur-sm rounded-2xl p-8 border border-primary/20 relative"
                            >
                                {/* Loading Overlay */}
                                {isGenerating && (
                                    <div className="absolute inset-0 bg-dark-card/80 backdrop-blur-sm rounded-2xl flex items-center justify-center z-10">
                                        <div className="text-center space-y-4">
                                            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
                                            <p className="text-text-light font-title text-lg">
                                                Generating your image...
                                            </p>
                                            <p className="text-text-light/60 text-sm">
                                                This may take 10-30 seconds
                                            </p>
                                        </div>
                                    </div>
                                )}
                                <div className="space-y-6">
                                    {/* Prompt Input */}
                                    <div>
                                        <label className="block text-lg font-title text-gradient mb-3">
                                            {t('generator.form.promptLabel')}
                                        </label>
                                        <textarea
                                            value={prompt}
                                            onChange={(e) => setPrompt(e.target.value)}
                                            placeholder={t('generator.form.promptPlaceholder')}
                                            className="w-full h-32 px-4 py-3 bg-dark-card/30 border border-primary/20 rounded-xl text-text-light placeholder-text-light/60 focus:outline-none focus:border-primary/40 resize-none"
                                            disabled={isGenerating}
                                        />
                                    </div>

                                    {/* Style Selection */}
                                    <div className="grid md:grid-cols-2 gap-6">
                                        <div>
                                            <label className="block text-lg font-title text-gradient mb-3">
                                                {t('generator.form.styleLabel')}
                                            </label>
                                            <select
                                                value={style}
                                                onChange={(e) => setStyle(e.target.value)}
                                                className="w-full px-4 py-3 bg-dark-card/30 border border-primary/20 rounded-xl text-text-light focus:outline-none focus:border-primary/40 select-dropdown"
                                                disabled={isGenerating}
                                            >
                                                <option value="photorealistic">{t('generator.form.styleOptions.photorealistic')}</option>
                                                <option value="artistic">{t('generator.form.styleOptions.artistic')}</option>
                                                <option value="illustration">{t('generator.form.styleOptions.illustration')}</option>
                                                <option value="abstract">{t('generator.form.styleOptions.abstract')}</option>
                                                <option value="fantasy">{t('generator.form.styleOptions.fantasy')}</option>
                                            </select>
                                        </div>

                                        <div>
                                            <label className="block text-lg font-title text-gradient mb-3">
                                                {t('generator.form.aspectRatioLabel')}
                                            </label>
                                            <select
                                                value={aspectRatio}
                                                onChange={(e) => setAspectRatio(e.target.value)}
                                                className="w-full px-4 py-3 bg-dark-card/30 border border-primary/20 rounded-xl text-text-light focus:outline-none focus:border-primary/40 select-dropdown"
                                                disabled={isGenerating}
                                            >
                                                <option value="square">{t('generator.form.aspectRatioOptions.square')}</option>
                                                <option value="portrait">{t('generator.form.aspectRatioOptions.portrait')}</option>
                                                <option value="landscape">{t('generator.form.aspectRatioOptions.landscape')}</option>
                                                <option value="wide">{t('generator.form.aspectRatioOptions.wide')}</option>
                                            </select>
                                        </div>
                                    </div>

                                    {/* Error Message */}
                                    {error && (
                                        <div className="bg-red-500/10 border border-red-500/30 rounded-xl p-4 text-red-400">
                                            {error}
                                        </div>
                                    )}

                                    {/* Generate Button */}
                                    <button
                                        onClick={handleGenerate}
                                        disabled={isGenerating || !prompt.trim()}
                                        className="w-full btn-primary text-lg py-4 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
                                    >
                                        {isGenerating && (
                                            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                                        )}
                                        {isGenerating ? t('generator.form.generatingButton') : t('generator.form.generateButton')}
                                    </button>

                                    {/* Error Display */}
                                    {error && (
                                        <motion.div
                                            initial={{ opacity: 0, y: 10 }}
                                            animate={{ opacity: 1, y: 0 }}
                                            className="bg-red-500/10 border border-red-500/20 rounded-xl p-4 text-center"
                                        >
                                            <p className="text-red-400 font-medium">⚠️ {error}</p>
                                        </motion.div>
                                    )}
                                </div>
                            </motion.div>
                        ) : (
                            <motion.div 
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.6 }}
                                className="bg-dark-card/50 backdrop-blur-sm rounded-2xl p-8 border border-primary/20"
                            >
                                <div className="text-center space-y-6">
                                    <h3 className="text-2xl font-title font-bold text-gradient">
                                        Generated Image
                                    </h3>
                                    
                                    {/* Image Display */}
                                    <div className="relative max-w-2xl mx-auto">
                                        <div className="bg-gradient-to-br from-primary/20 to-secondary/20 rounded-2xl p-4 border border-primary/20">
                                            <div className="relative overflow-hidden rounded-xl">
                                                <img
                                                    src={generatedImage.dataUrl || generatedImage.imageUrl}
                                                    alt={`Generated image: ${generatedImage.prompt}`}
                                                    className="w-full h-auto max-h-[600px] object-contain rounded-xl"
                                                    onError={(e) => {
                                                        // Fallback to dataUrl if imageUrl fails
                                                        const target = e.target as HTMLImageElement;
                                                        if (generatedImage.dataUrl && target.src !== generatedImage.dataUrl) {
                                                            target.src = generatedImage.dataUrl;
                                                        }
                                                    }}
                                                />
                                                <div className="absolute bottom-2 right-2 bg-black/50 backdrop-blur-sm rounded-lg px-3 py-1">
                                                    <p className="text-white text-xs">
                                                        {generatedImage.style} | {generatedImage.aspectRatio}
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    {/* Prompt Display */}
                                    <div className="bg-dark-card/30 rounded-xl p-4 border border-primary/10">
                                        <p className="text-text-light font-title text-sm mb-2">Generated from prompt:</p>
                                        <p className="text-text-light/80 italic">&ldquo;{generatedImage.prompt}&rdquo;</p>
                                    </div>

                                    {/* Action Buttons */}
                                    <div className="flex flex-col sm:flex-row gap-4 justify-center">
                                        <button
                                            onClick={handleDownload}
                                            className="btn-primary flex items-center gap-2 px-6 py-3"
                                        >
                                            <span>📥</span>
                                            {t('generator.form.downloadButton')}
                                        </button>
                                        <button
                                            onClick={handleNewImage}
                                            className="btn-secondary flex items-center gap-2 px-6 py-3"
                                        >
                                            <span>✨</span>
                                            {t('generator.form.newImageButton')}
                                        </button>
                                    </div>
                                </div>
                            </motion.div>
                        )}
                    </div>
                </div>
            </section>

            {/* Features Section */}
            <section className="py-16 sm:py-20">
                <div className="container mx-auto px-4">
                    <div className="max-w-6xl mx-auto">
                        <div className="text-center mb-16">
                            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-display font-bold text-gradient mb-6">
                                {t('features.title')}
                            </h2>
                            <p className="text-xl text-primary font-title">
                                {t('features.subtitle')}
                            </p>
                        </div>

                        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
                            {['quality', 'speed', 'variety', 'commercial'].map((feature) => (
                                <motion.div
                                    key={feature}
                                    initial={{ opacity: 0, y: 20 }}
                                    whileInView={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.6 }}
                                    className="text-center"
                                >
                                    <div className="w-16 h-16 bg-gradient-to-br from-primary to-secondary rounded-2xl flex items-center justify-center mx-auto mb-6">
                                        {feature === 'quality' ? <Star className="w-8 h-8 text-white" /> :
                                         feature === 'speed' ? <Zap className="w-8 h-8 text-white" /> :
                                         feature === 'variety' ? <Palette className="w-8 h-8 text-white" /> :
                                         <Briefcase className="w-8 h-8 text-white" />}
                                    </div>
                                    <h3 className="text-xl font-title font-bold text-gradient mb-4">
                                        {t(`features.items.${feature}.title`)}
                                    </h3>
                                    <p className="text-text-light leading-relaxed">
                                        {t(`features.items.${feature}.description`)}
                                    </p>
                                </motion.div>
                            ))}
                        </div>
                    </div>
                </div>
            </section>

            {/* Prompt Examples Section */}
            <section className="py-16 sm:py-20 bg-dark-card/30">
                <div className="container mx-auto px-4">
                    <div className="max-w-6xl mx-auto">
                        <div className="text-center mb-16">
                            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-display font-bold text-gradient mb-6">
                                {t('examples.title')}
                            </h2>
                            <p className="text-xl text-primary font-title mb-8">
                                {t('examples.subtitle')}
                            </p>
                            <p className="text-lg text-text-light max-w-4xl mx-auto leading-relaxed">
                                {t('examples.description')}
                            </p>
                        </div>

                        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                            {Array.from({ length: 6 }, (_, i) => (
                                <motion.div
                                    key={i}
                                    initial={{ opacity: 0, y: 20 }}
                                    whileInView={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.6, delay: i * 0.1 }}
                                    className="bg-dark-card/50 rounded-xl p-6 border border-primary/20 hover:border-primary/40 transition-all duration-300 cursor-pointer"
                                    onClick={() => setPrompt(t(`examples.items.${i}.prompt`))}
                                >
                                    <div className="mb-4">
                                        <div className="flex items-center gap-2 mb-3">
                                            <ImageIcon className="w-5 h-5 text-primary" />
                                            <span className="text-xs font-semibold text-primary uppercase tracking-wide">
                                                {t(`examples.items.${i}.category`)}
                                            </span>
                                        </div>
                                        <h4 className="font-title font-semibold text-text-light mb-3 text-base sm:text-lg">
                                            {t(`examples.items.${i}.title`)}
                                        </h4>
                                        <p className="text-text-light text-sm leading-relaxed mb-4 bg-dark/30 rounded-lg p-3 border-l-2 border-primary/30">
                                            &ldquo;{t(`examples.items.${i}.prompt`)}&rdquo;
                                        </p>
                                        <div className="text-xs text-text-muted">
                                            {t('examples.clickToTry')}
                                        </div>
                                    </div>
                                </motion.div>
                            ))}
                        </div>
                    </div>
                </div>
            </section>

            {/* Tips Section */}
            <section className="py-16 sm:py-20">
                <div className="container mx-auto px-4">
                    <div className="max-w-6xl mx-auto">
                        <div className="text-center mb-16">
                            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-display font-bold text-gradient mb-6">
                                {t('tips.title')}
                            </h2>
                            <p className="text-xl text-primary font-title">
                                {t('tips.subtitle')}
                            </p>
                        </div>

                        <div className="grid md:grid-cols-2 gap-8">
                            {Array.from({ length: 4 }, (_, i) => (
                                <motion.div
                                    key={i}
                                    initial={{ opacity: 0, y: 20 }}
                                    whileInView={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.6, delay: i * 0.1 }}
                                    className="bg-dark-card/30 rounded-2xl p-6 border border-primary/10 hover:border-primary/30 transition-all duration-300"
                                >
                                    <h3 className="text-xl font-title font-bold text-gradient mb-3">
                                        {t(`tips.items.${i}.title`)}
                                    </h3>
                                    <p className="text-text-light leading-relaxed">
                                        {t(`tips.items.${i}.description`)}
                                    </p>
                                </motion.div>
                            ))}
                        </div>
                    </div>
                </div>
            </section>

            {/* CTA Section */}
            <section className="py-16 sm:py-20 lg:py-24 bg-dark-card/30">
                <div className="container mx-auto px-4">
                    <div className="max-w-4xl mx-auto text-center space-y-8">
                        <motion.h2 
                            initial={{ opacity: 0, y: 20 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.6 }}
                            className="text-3xl sm:text-4xl lg:text-5xl font-display font-bold text-gradient"
                        >
                            {t('cta.title')}
                        </motion.h2>
                        <motion.p 
                            initial={{ opacity: 0, y: 20 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.6, delay: 0.1 }}
                            className="text-xl text-primary font-title"
                        >
                            {t('cta.subtitle')}
                        </motion.p>
                        <motion.p 
                            initial={{ opacity: 0, y: 20 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.6, delay: 0.2 }}
                            className="text-lg text-text-light leading-relaxed"
                        >
                            {t('cta.description')}
                        </motion.p>

                        <motion.div 
                            initial={{ opacity: 0, y: 20 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.6, delay: 0.3 }}
                            className="mt-12"
                        >
                            <Link
                                href="/imagen"
                                className="btn-primary inline-flex items-center gap-3 text-lg px-8 py-4"
                            >
                                {t('cta.button')}
                                <span className="text-xl">→</span>
                            </Link>
                        </motion.div>
                    </div>
                </div>
            </section>
        </>
    );
}