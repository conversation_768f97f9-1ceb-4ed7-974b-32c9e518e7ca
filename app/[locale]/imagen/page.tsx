import { Metadata } from 'next';
import { getTranslations, setRequestLocale } from 'next-intl/server';
import { notFound } from 'next/navigation';
import { routing } from '@/i18n/routing';
import ImagenContent from './ImagenContent';

type Props = {
    params: Promise<{ locale: string }>;
};

export async function generateMetadata({ params }: Props): Promise<Metadata> {
    const { locale } = await params;

    if (!routing.locales.includes(locale as any)) {
        notFound();
    }

    const t = await getTranslations({ locale, namespace: 'pages.imagen.metadata' });

    return {
        title: t('title'),
        description: t('description'),
        keywords: 'Google Imagen, Google Vertex AI, Imagen AI model, Google AI image generator, text to image Google, Google Cloud AI, Vertex AI Imagen, Google machine learning, AI image generation Google, Google generative AI, Imagen 2.0, Google text-to-image',
        openGraph: {
            title: t('title'),
            description: t('description'),
            url: `https://a3i.ai/${locale}/imagen`,
            siteName: 'a3i.ai',
            images: [
                {
                    url: 'https://a3i.ai/hero-background-ai-abstract-blue-purple-gradient.webp',
                    width: 1200,
                    height: 630,
                    alt: 'a3i.ai Imagen - AI Image Generator',
                },
            ],
            locale: locale,
            type: 'website',
        },
        twitter: {
            card: 'summary_large_image',
            title: t('title'),
            description: t('description'),
            images: ['https://a3i.ai/hero-background-ai-abstract-blue-purple-gradient.webp'],
        },
        alternates: {
            canonical: `https://a3i.ai/${locale}/imagen`,
            languages: {
                'en': 'https://a3i.ai/en/imagen',
                'zh': 'https://a3i.ai/zh/imagen',
            },
        },
    };
}

export default async function ImagenPage({ params }: Props) {
    const { locale } = await params;

    if (!routing.locales.includes(locale as any)) {
        notFound();
    }

    setRequestLocale(locale);
    const t = await getTranslations('pages.imagen.content');

    return (
        <>
            {/* Structured Data */}
            <script
                type="application/ld+json"
                dangerouslySetInnerHTML={{
                    __html: JSON.stringify({
                        "@context": "https://schema.org",
                        "@type": "SoftwareApplication",
                        "name": "Google Imagen AI Generator - Powered by Vertex AI",
                        "description": "Professional AI image generator powered by Google's advanced Imagen model via Vertex AI. Create high-quality images from text using Google's state-of-the-art generative AI technology.",
                        "url": `https://a3i.ai/${locale}/imagen`,
                        "applicationCategory": "DesignApplication",
                        "operatingSystem": "Web Browser",
                        "offers": {
                            "@type": "Offer",
                            "price": "0",
                            "priceCurrency": "USD"
                        },
                        "creator": {
                            "@type": "Organization",
                            "name": "a3i.ai",
                            "url": "https://a3i.ai"
                        },
                        "provider": {
                            "@type": "Organization",
                            "name": "Google Cloud",
                            "url": "https://cloud.google.com"
                        },
                        "featureList": [
                            "Google Imagen AI Model",
                            "Vertex AI Integration",
                            "High-Resolution Image Generation",
                            "Advanced Text-to-Image Conversion",
                            "Google's State-of-the-Art AI Technology",
                            "Professional Quality Output",
                            "Fast Generation Speed"
                        ],
                        "about": {
                            "@type": "Thing",
                            "name": "Google Imagen",
                            "description": "Google's advanced text-to-image AI model that generates high-quality, photorealistic images from natural language descriptions"
                        }
                    })
                }}
            />

            <main className="min-h-screen pt-20 sm:pt-24">
                <ImagenContent />
            </main>
        </>
    );
}