import { Metadata } from 'next';
import { getTranslations, setRequestLocale } from 'next-intl/server';
import { notFound } from 'next/navigation';
import { routing, Link, Pathnames } from '@/i18n/routing';

type Props = {
    params: Promise<{ locale: string }>;
};

export async function generateMetadata({ params }: Props): Promise<Metadata> {
    const { locale } = await params;

    if (!routing.locales.includes(locale as any)) {
        notFound();
    }

    const t = await getTranslations({ locale, namespace: 'pages.gallery.metadata' });

    return {
        title: t('title'),
        description: t('description'),
        keywords: 'a3i.ai gallery, AI generated art, AI images, AI avatars, visual creations, community gallery, create beyond imagination, AI artwork, digital art',
        openGraph: {
            title: t('title'),
            description: t('description'),
            url: `https://a3i.ai/${locale}/gallery`,
            siteName: 'a3i.ai',
            images: [
                {
                    url: 'https://a3i.ai/hero-background-ai-abstract-blue-purple-gradient.webp',
                    width: 1200,
                    height: 630,
                    alt: 'a3i.ai Gallery - AI Generated Art & Creations',
                },
            ],
            locale: locale,
            type: 'website',
        },
        twitter: {
            card: 'summary_large_image',
            title: t('title'),
            description: t('description'),
            images: ['https://a3i.ai/hero-background-ai-abstract-blue-purple-gradient.webp'],
        },
        alternates: {
            canonical: `https://a3i.ai/${locale}/gallery`,
            languages: {
                'en': 'https://a3i.ai/en/gallery',
                'zh': 'https://a3i.ai/zh/gallery',
            },
        },
    };
}

export default async function Gallery({ params }: Props) {
    const { locale } = await params;

    if (!routing.locales.includes(locale as any)) {
        notFound();
    }

    setRequestLocale(locale);
    const t = await getTranslations('pages.gallery.content');

    return (
        <>
            {/* Structured Data */}
            <script
                type="application/ld+json"
                dangerouslySetInnerHTML={{
                    __html: JSON.stringify({
                        "@context": "https://schema.org",
                        "@type": "ImageGallery",
                        "name": "a3i.ai Gallery",
                        "description": "Explore stunning AI-generated images, avatars, and visual art created by the a3i.ai community",
                        "url": `https://a3i.ai/${locale}/gallery`,
                        "creator": {
                            "@type": "Organization",
                            "name": "a3i.ai"
                        },
                        "about": {
                            "@type": "Thing",
                            "name": "AI Generated Art"
                        }
                    })
                }}
            />

            <main className="min-h-screen pt-20 sm:pt-24">
                {/* Hero Section */}
                <section className="relative py-16 sm:py-20 lg:py-24">
                    <div className="container mx-auto px-4">
                        <div className="max-w-4xl mx-auto text-center space-y-6">
                            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-display font-bold text-gradient leading-tight">
                                {t('hero.title')}
                            </h1>
                            <p className="text-xl sm:text-2xl text-primary font-title">
                                {t('hero.subtitle')}
                            </p>
                            <p className="text-lg text-text-light max-w-3xl mx-auto leading-relaxed">
                                {t('hero.description')}
                            </p>
                        </div>
                    </div>
                </section>

                {/* Featured Section */}
                <section className="py-16 sm:py-20 bg-dark-card/30">
                    <div className="container mx-auto px-4">
                        <div className="max-w-6xl mx-auto">
                            <div className="text-center mb-16">
                                <h2 className="text-3xl sm:text-4xl lg:text-5xl font-display font-bold text-gradient mb-6">
                                    {t('featured.title')}
                                </h2>
                                <p className="text-xl text-primary font-title mb-8">
                                    {t('featured.subtitle')}
                                </p>
                                <p className="text-lg text-text-light max-w-4xl mx-auto leading-relaxed">
                                    {t('featured.description')}
                                </p>
                            </div>

                            {/* AI Tools Guide Grid */}
                            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                                {[
                                    { title: "Image Generator", description: "Create stunning images from text prompts using advanced AI technology", icon: "🖼️", link: "/image-generator" },
                                    { title: "Avatar Creator", description: "Generate personalized avatars for profiles, gaming, and social media", icon: "👤", link: "/avatar-creator" },
                                    { title: "Image to Text", description: "Extract text and descriptions from images using AI vision technology", icon: "📝", link: "/image-to-text" },
                                    { title: "Prompt Engineering", description: "Learn how to write effective prompts for better AI image generation", icon: "💡", link: "#tips" },
                                    { title: "Style Guide", description: "Explore different artistic styles and techniques for AI image creation", icon: "🎨", link: "#styles" },
                                    { title: "Best Practices", description: "Tips and tricks for getting the most out of AI image generation tools", icon: "⭐", link: "#practices" }
                                ].map((item, i) => (
                                    <div
                                        key={i}
                                        className="bg-dark-card/50 rounded-2xl p-6 border border-primary/20 hover:border-primary/40 transition-all duration-300 group cursor-pointer"
                                    >
                                        <div className="text-center">
                                            <div className="text-4xl mb-4 group-hover:scale-110 transition-transform duration-300">
                                                {item.icon}
                                            </div>
                                            <h3 className="text-text-light font-title font-semibold text-lg mb-3">
                                                {item.title}
                                            </h3>
                                            <p className="text-text-muted text-sm leading-relaxed">
                                                {item.description}
                                            </p>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>
                    </div>
                </section>

                {/* Categories Section */}
                <section className="py-16 sm:py-20">
                    <div className="container mx-auto px-4">
                        <div className="max-w-6xl mx-auto">
                            <div className="text-center mb-16">
                                <h2 className="text-3xl sm:text-4xl lg:text-5xl font-display font-bold text-gradient mb-6">
                                    {t('categories.title')}
                                </h2>
                                <p className="text-xl text-primary font-title">
                                    {t('categories.subtitle')}
                                </p>
                            </div>

                            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                                {['portraits', 'landscapes', 'abstract', 'commercial', 'fantasy', 'fashion'].map((category, index) => (
                                    <div
                                        key={category}
                                        className="bg-dark-card/30 rounded-2xl p-6 border border-primary/10 hover:border-primary/30 transition-all duration-300 group cursor-pointer"
                                    >
                                        <div className="text-4xl mb-4 group-hover:scale-110 transition-transform duration-300">
                                            {index === 0 ? '👤' : index === 1 ? '🌄' : index === 2 ? '🎭' : index === 3 ? '💼' : index === 4 ? '🐉' : '👗'}
                                        </div>
                                        <h3 className="text-xl font-title font-bold text-gradient mb-3">
                                            {t(`categories.items.${category}.title`)}
                                        </h3>
                                        <p className="text-text-light text-sm leading-relaxed mb-4">
                                            {t(`categories.items.${category}.description`)}
                                        </p>
                                        <div className="text-primary font-title text-sm">
                                            {t(`categories.items.${category}.count`)}
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>
                    </div>
                </section>

                {/* Stats Section */}
                <section className="py-16 sm:py-20 bg-dark-card/30">
                    <div className="container mx-auto px-4">
                        <div className="max-w-6xl mx-auto">
                            <div className="text-center mb-16">
                                <h2 className="text-3xl sm:text-4xl lg:text-5xl font-display font-bold text-gradient mb-6">
                                    {t('stats.title')}
                                </h2>
                                <p className="text-xl text-primary font-title">
                                    {t('stats.subtitle')}
                                </p>
                            </div>

                            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
                                {['totalImages', 'activeUsers', 'countries', 'satisfaction'].map((metric) => (
                                    <div key={metric} className="text-center">
                                        <div className="text-4xl sm:text-5xl font-display font-bold text-gradient mb-4">
                                            {t(`stats.metrics.${metric}.value`)}
                                        </div>
                                        <p className="text-text-light font-title">
                                            {t(`stats.metrics.${metric}.label`)}
                                        </p>
                                    </div>
                                ))}
                            </div>
                        </div>
                    </div>
                </section>

                {/* Showcase Section */}
                <section className="py-16 sm:py-20">
                    <div className="container mx-auto px-4">
                        <div className="max-w-6xl mx-auto">
                            <div className="text-center mb-16">
                                <h2 className="text-3xl sm:text-4xl lg:text-5xl font-display font-bold text-gradient mb-6">
                                    {t('showcase.title')}
                                </h2>
                                <p className="text-xl text-primary font-title">
                                    {t('showcase.subtitle')}
                                </p>
                            </div>

                            <div className="grid lg:grid-cols-3 gap-8">
                                {Array.from({ length: 3 }, (_, i) => (
                                    <div
                                        key={i}
                                        className="bg-dark-card/30 rounded-2xl p-8 border border-primary/10"
                                    >
                                        <div className="text-center mb-6">
                                            <div className="w-16 h-16 bg-gradient-to-br from-primary to-secondary rounded-full mx-auto mb-4"></div>
                                            <h3 className="font-title font-bold text-gradient text-lg">
                                                {t(`showcase.testimonials.${i}.author`)}
                                            </h3>
                                            <p className="text-primary text-sm">
                                                {t(`showcase.testimonials.${i}.role`)}
                                            </p>
                                        </div>
                                        <p className="text-text-light leading-relaxed italic">
                                            &ldquo;{t(`showcase.testimonials.${i}.content`)}&rdquo;
                                        </p>
                                    </div>
                                ))}
                            </div>
                        </div>
                    </div>
                </section>

                {/* Trending Section */}
                <section className="py-16 sm:py-20 bg-dark-card/30">
                    <div className="container mx-auto px-4">
                        <div className="max-w-6xl mx-auto">
                            <div className="text-center mb-16">
                                <h2 className="text-3xl sm:text-4xl lg:text-5xl font-display font-bold text-gradient mb-6">
                                    {t('trending.title')}
                                </h2>
                                <p className="text-xl text-primary font-title">
                                    {t('trending.subtitle')}
                                </p>
                            </div>

                            <div className="flex flex-wrap gap-4 justify-center">
                                {Array.from({ length: 8 }, (_, i) => (
                                    <span
                                        key={i}
                                        className="bg-dark-card/50 border border-primary/20 rounded-full px-6 py-3 text-text-light hover:text-primary hover:border-primary/40 transition-all duration-300 cursor-pointer"
                                    >
                                        {t(`trending.tags.${i}`)}
                                    </span>
                                ))}
                            </div>
                        </div>
                    </div>
                </section>

                {/* CTA Section */}
                <section className="py-16 sm:py-20 lg:py-24">
                    <div className="container mx-auto px-4">
                        <div className="max-w-4xl mx-auto text-center space-y-8">
                            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-display font-bold text-gradient">
                                {t('cta.title')}
                            </h2>
                            <p className="text-xl text-primary font-title">
                                {t('cta.subtitle')}
                            </p>
                            <p className="text-lg text-text-light leading-relaxed">
                                {t('cta.description')}
                            </p>

                            <div className="flex flex-col sm:flex-row gap-4 justify-center mt-12">
                                <Link
                                    href={"/" as Pathnames}
                                    className="btn-primary inline-flex items-center gap-3 text-lg px-8 py-4"
                                >
                                    {t('cta.buttons.primary')}
                                    <span className="text-xl">→</span>
                                </Link>
                                <Link
                                    href={"/gallery" as Pathnames}
                                    className="btn-secondary inline-flex items-center gap-3 text-lg px-8 py-4"
                                >
                                    {t('cta.buttons.secondary')}
                                </Link>
                            </div>
                        </div>
                    </div>
                </section>
            </main>
        </>
    );
} 