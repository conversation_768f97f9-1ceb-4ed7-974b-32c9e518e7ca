'use client';

import { motion } from 'framer-motion';
import { useTranslations } from 'next-intl';
import { Link, Pathnames } from '@/i18n/routing';

export default function AboutContent() {
    const t = useTranslations('pages.about.content');

    const fadeInUp = {
        hidden: { opacity: 0, y: 30 },
        visible: {
            opacity: 1,
            y: 0,
            transition: {
                duration: 0.6,
                ease: "easeOut"
            }
        }
    };

    const staggerChildren = {
        hidden: {},
        visible: {
            transition: {
                staggerChildren: 0.1
            }
        }
    };

    return (
        <main className="min-h-screen pt-20 sm:pt-24">
            {/* Hero Section */}
            <section className="relative py-16 sm:py-20 lg:py-24">
                <div className="container mx-auto px-4">
                    <div className="max-w-4xl mx-auto text-center">
                        <motion.div
                            initial="hidden"
                            animate="visible"
                            variants={staggerChildren}
                            className="space-y-6"
                        >
                            <motion.h1
                                variants={fadeInUp}
                                className="text-4xl sm:text-5xl lg:text-6xl font-display font-bold text-gradient leading-tight"
                            >
                                {t('hero.title')}
                            </motion.h1>
                            <motion.p
                                variants={fadeInUp}
                                className="text-xl sm:text-2xl text-primary font-title"
                            >
                                {t('hero.subtitle')}
                            </motion.p>
                            <motion.p
                                variants={fadeInUp}
                                className="text-lg text-text-light max-w-3xl mx-auto leading-relaxed"
                            >
                                {t('hero.description')}
                            </motion.p>
                        </motion.div>
                    </div>
                </div>
            </section>

            {/* Mission Section */}
            <section className="py-16 sm:py-20 bg-dark-card/30">
                <div className="container mx-auto px-4">
                    <div className="max-w-6xl mx-auto">
                        <motion.div
                            initial="hidden"
                            whileInView="visible"
                            viewport={{ once: true }}
                            variants={staggerChildren}
                            className="text-center mb-16"
                        >
                            <motion.h2
                                variants={fadeInUp}
                                className="text-3xl sm:text-4xl lg:text-5xl font-display font-bold text-gradient mb-6"
                            >
                                {t('mission.title')}
                            </motion.h2>
                            <motion.p
                                variants={fadeInUp}
                                className="text-xl text-primary font-title mb-8"
                            >
                                {t('mission.subtitle')}
                            </motion.p>
                            <motion.p
                                variants={fadeInUp}
                                className="text-lg text-text-light max-w-4xl mx-auto leading-relaxed"
                            >
                                {t('mission.description')}
                            </motion.p>
                        </motion.div>

                        <motion.div
                            initial="hidden"
                            whileInView="visible"
                            viewport={{ once: true }}
                            variants={staggerChildren}
                            className="grid md:grid-cols-3 gap-8"
                        >
                            {[
                                { key: 'innovation', icon: '🚀' },
                                { key: 'accessibility', icon: '🌍' },
                                { key: 'quality', icon: '⭐' }
                            ].map((value) => (
                                <motion.div
                                    key={value.key}
                                    variants={fadeInUp}
                                    className="bg-dark-card/50 backdrop-blur-sm rounded-2xl p-8 border border-primary/20 hover:border-primary/40 transition-all duration-300"
                                >
                                    <div className="text-4xl mb-4">{value.icon}</div>
                                    <h3 className="text-xl font-title font-bold text-gradient mb-4">
                                        {t(`mission.values.${value.key}.title`)}
                                    </h3>
                                    <p className="text-text-light leading-relaxed">
                                        {t(`mission.values.${value.key}.description`)}
                                    </p>
                                </motion.div>
                            ))}
                        </motion.div>
                    </div>
                </div>
            </section>

            {/* Story Section */}
            <section className="py-16 sm:py-20">
                <div className="container mx-auto px-4">
                    <div className="max-w-4xl mx-auto">
                        <motion.div
                            initial="hidden"
                            whileInView="visible"
                            viewport={{ once: true }}
                            variants={staggerChildren}
                            className="text-center mb-16"
                        >
                            <motion.h2
                                variants={fadeInUp}
                                className="text-3xl sm:text-4xl lg:text-5xl font-display font-bold text-gradient mb-6"
                            >
                                {t('story.title')}
                            </motion.h2>
                            <motion.p
                                variants={fadeInUp}
                                className="text-xl text-primary font-title"
                            >
                                {t('story.subtitle')}
                            </motion.p>
                        </motion.div>

                        <motion.div
                            initial="hidden"
                            whileInView="visible"
                            viewport={{ once: true }}
                            variants={staggerChildren}
                            className="space-y-12"
                        >
                            {Array.from({ length: 3 }, (_, i) => (
                                <motion.div
                                    key={i}
                                    variants={fadeInUp}
                                    className="flex flex-col md:flex-row items-start gap-8 bg-dark-card/30 rounded-2xl p-8 border border-primary/10"
                                >
                                    <div className="flex-shrink-0">
                                        <div className="w-20 h-20 bg-gradient-to-br from-primary to-secondary rounded-2xl flex items-center justify-center text-white font-bold text-lg">
                                            {t(`story.content.${i}.year`)}
                                        </div>
                                    </div>
                                    <div>
                                        <h3 className="text-2xl font-title font-bold text-gradient mb-4">
                                            {t(`story.content.${i}.title`)}
                                        </h3>
                                        <p className="text-text-light leading-relaxed">
                                            {t(`story.content.${i}.description`)}
                                        </p>
                                    </div>
                                </motion.div>
                            ))}
                        </motion.div>
                    </div>
                </div>
            </section>

            {/* Technology Section */}
            <section className="py-16 sm:py-20 bg-dark-card/30">
                <div className="container mx-auto px-4">
                    <div className="max-w-6xl mx-auto">
                        <motion.div
                            initial="hidden"
                            whileInView="visible"
                            viewport={{ once: true }}
                            variants={staggerChildren}
                            className="text-center mb-16"
                        >
                            <motion.h2
                                variants={fadeInUp}
                                className="text-3xl sm:text-4xl lg:text-5xl font-display font-bold text-gradient mb-6"
                            >
                                {t('technology.title')}
                            </motion.h2>
                            <motion.p
                                variants={fadeInUp}
                                className="text-xl text-primary font-title mb-8"
                            >
                                {t('technology.subtitle')}
                            </motion.p>
                            <motion.p
                                variants={fadeInUp}
                                className="text-lg text-text-light max-w-4xl mx-auto leading-relaxed"
                            >
                                {t('technology.description')}
                            </motion.p>
                        </motion.div>

                        <motion.div
                            initial="hidden"
                            whileInView="visible"
                            viewport={{ once: true }}
                            variants={staggerChildren}
                            className="grid md:grid-cols-3 gap-8"
                        >
                            {[
                                { key: 'algorithms', icon: '🧠' },
                                { key: 'performance', icon: '⚡' },
                                { key: 'scalability', icon: '📈' }
                            ].map((feature) => (
                                <motion.div
                                    key={feature.key}
                                    variants={fadeInUp}
                                    className="bg-dark-card/50 backdrop-blur-sm rounded-2xl p-8 border border-primary/20 hover:border-primary/40 transition-all duration-300"
                                >
                                    <div className="text-4xl mb-4">{feature.icon}</div>
                                    <h3 className="text-xl font-title font-bold text-gradient mb-4">
                                        {t(`technology.features.${feature.key}.title`)}
                                    </h3>
                                    <p className="text-text-light leading-relaxed">
                                        {t(`technology.features.${feature.key}.description`)}
                                    </p>
                                </motion.div>
                            ))}
                        </motion.div>
                    </div>
                </div>
            </section>

            {/* Team & CTA Section */}
            <section className="py-16 sm:py-20 lg:py-24">
                <div className="container mx-auto px-4">
                    <div className="max-w-4xl mx-auto text-center">
                        <motion.div
                            initial="hidden"
                            whileInView="visible"
                            viewport={{ once: true }}
                            variants={staggerChildren}
                            className="space-y-8"
                        >
                            <motion.h2
                                variants={fadeInUp}
                                className="text-3xl sm:text-4xl lg:text-5xl font-display font-bold text-gradient"
                            >
                                {t('team.title')}
                            </motion.h2>
                            <motion.p
                                variants={fadeInUp}
                                className="text-xl text-primary font-title"
                            >
                                {t('team.subtitle')}
                            </motion.p>
                            <motion.p
                                variants={fadeInUp}
                                className="text-lg text-text-light leading-relaxed"
                            >
                                {t('team.description')}
                            </motion.p>

                            <motion.div
                                variants={fadeInUp}
                                className="bg-gradient-to-r from-primary/20 to-secondary/20 backdrop-blur-sm rounded-2xl p-8 border border-primary/20 mt-16"
                            >
                                <h3 className="text-2xl font-title font-bold text-gradient mb-4">
                                    {t('team.cta.title')}
                                </h3>
                                <p className="text-text-light mb-8 leading-relaxed">
                                    {t('team.cta.description')}
                                </p>
                                <Link
                                    href={"/" as Pathnames}
                                    className="btn-primary inline-flex items-center gap-3 text-lg px-8 py-4"
                                >
                                    {t('team.cta.button')}
                                    <span className="text-xl">→</span>
                                </Link>
                            </motion.div>
                        </motion.div>
                    </div>
                </div>
            </section>
        </main>
    );
} 