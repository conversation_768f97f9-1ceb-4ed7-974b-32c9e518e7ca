import { Metadata } from 'next';
import { getTranslations, setRequestLocale } from 'next-intl/server';
import { notFound } from 'next/navigation';
import { routing } from '@/i18n/routing';
import AboutContent from './AboutContent';

type Props = {
    params: Promise<{ locale: string }>;
};

export async function generateMetadata({ params }: Props): Promise<Metadata> {
    const { locale } = await params;

    if (!routing.locales.includes(locale as any)) {
        notFound();
    }

    const t = await getTranslations({ locale, namespace: 'pages.about.metadata' });

    return {
        title: t('title'),
        description: t('description'),
        keywords: 'a3i.ai, AI visual creation, artificial intelligence, image generation, avatar creator, create beyond imagination, AI platform, visual AI, AI tools',
        openGraph: {
            title: t('title'),
            description: t('description'),
            url: `https://a3i.ai/${locale}/about`,
            siteName: 'a3i.ai',
            images: [
                {
                    url: 'https://a3i.ai/hero-background-ai-abstract-blue-purple-gradient.webp',
                    width: 1200,
                    height: 630,
                    alt: 'a3i.ai - AI Visual Creation Platform',
                },
            ],
            locale: locale,
            type: 'website',
        },
        twitter: {
            card: 'summary_large_image',
            title: t('title'),
            description: t('description'),
            images: ['https://a3i.ai/hero-background-ai-abstract-blue-purple-gradient.webp'],
        },
        alternates: {
            canonical: `https://a3i.ai/${locale}/about`,
            languages: {
                'en': 'https://a3i.ai/en/about',
                'zh': 'https://a3i.ai/zh/about',
            },
        },
    };
}

export default async function About({ params }: Props) {
    const { locale } = await params;

    if (!routing.locales.includes(locale as any)) {
        notFound();
    }

    setRequestLocale(locale);

    return (
        <>
            {/* Structured Data */}
            <script
                type="application/ld+json"
                dangerouslySetInnerHTML={{
                    __html: JSON.stringify({
                        "@context": "https://schema.org",
                        "@type": "AboutPage",
                        "name": "About a3i.ai",
                        "description": "Learn about a3i.ai's mission to revolutionize visual creation with advanced AI technology",
                        "url": `https://a3i.ai/${locale}/about`,
                        "mainEntity": {
                            "@type": "Organization",
                            "name": "a3i.ai",
                            "url": "https://a3i.ai",
                            "logo": "https://a3i.ai/logo.webp",
                            "slogan": "Create Beyond Imagination",
                            "foundingDate": "2024",
                            "description": "a3i.ai is a leading AI visual creation platform that empowers creators worldwide to transform their imagination into stunning reality."
                        }
                    })
                }}
            />
            <AboutContent />
        </>
    );
} 