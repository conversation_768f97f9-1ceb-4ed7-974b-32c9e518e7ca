'use client';

import { useState } from 'react';
import { useTranslations } from 'next-intl';
import { motion } from 'framer-motion';
import { Smartphone, Gamepad2, Briefcase, Monitor, Megaphone, Palette, User, UserCircle } from 'lucide-react';

interface GeneratedAvatar {
    success: boolean;
    imageUrl: string;
    dataUrl?: string;
    prompt: string;
    style: string;
    aspectRatio: string;
    dimensions: {
        width: number;
        height: number;
    };
}

export default function AvatarCreatorContent() {
    const t = useTranslations('pages.avatarCreator.content');
    const [prompt, setPrompt] = useState('');
    const [style, setStyle] = useState('photorealistic');
    const [aspectRatio, setAspectRatio] = useState('square');
    const [isGenerating, setIsGenerating] = useState(false);
    const [generatedAvatar, setGeneratedAvatar] = useState<GeneratedAvatar | null>(null);
    const [error, setError] = useState('');

    const enhanceAvatarPrompt = (userPrompt: string, selectedStyle: string): string => {
        // Add avatar-specific enhancements to the prompt
        const avatarEnhancements = {
            photorealistic: 'professional headshot, portrait photography, high quality, detailed facial features, clean background',
            artistic: 'artistic portrait, stylized character art, expressive, creative interpretation',
            illustration: 'character illustration, cartoon style, clean design, profile picture style',
            abstract: 'stylized avatar, geometric character design, modern minimalist portrait',
            fantasy: 'fantasy character portrait, detailed character design, imaginative avatar'
        };

        const enhancement = avatarEnhancements[selectedStyle as keyof typeof avatarEnhancements] || avatarEnhancements.photorealistic;
        return `${userPrompt}, ${enhancement}, avatar style, character portrait`;
    };

    const handleGenerate = async () => {
        if (!prompt.trim()) {
            setError(t('generator.form.promptRequired'));
            return;
        }

        setIsGenerating(true);
        setError('');

        try {
            // Enhance the prompt for avatar generation
            const enhancedPrompt = enhanceAvatarPrompt(prompt.trim(), style);

            const response = await fetch('/api/imagen', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    prompt: enhancedPrompt,
                    style,
                    aspectRatio,
                }),
            });

            const data = await response.json();

            if (!response.ok) {
                let errorMessage = 'Failed to generate avatar. Please try again.';
                
                if (response.status === 400) {
                    errorMessage = data.error || 'Invalid request. Please check your description and try again.';
                } else if (response.status === 401 || response.status === 403) {
                    errorMessage = 'Authentication error. Please refresh the page and try again.';
                } else if (response.status === 429) {
                    errorMessage = 'Too many requests. Please wait a moment and try again.';
                } else if (response.status >= 500) {
                    errorMessage = 'Server error. Please try again in a few moments.';
                } else {
                    errorMessage = data.error || errorMessage;
                }
                
                throw new Error(errorMessage);
            }

            if (!data.success) {
                throw new Error(data.error || 'Avatar generation failed');
            }

            setGeneratedAvatar(data);
        } catch (err) {
            console.error('Error generating avatar:', err);
            if (err instanceof Error) {
                setError(err.message);
            } else {
                setError('An unexpected error occurred. Please try again.');
            }
        } finally {
            setIsGenerating(false);
        }
    };

    const handleDownload = () => {
        if (generatedAvatar) {
            const link = document.createElement('a');
            link.href = generatedAvatar.dataUrl || generatedAvatar.imageUrl;
            link.download = `a3i-avatar-${Date.now()}.png`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    };

    const handleGenerateAnother = () => {
        setGeneratedAvatar(null);
        setError('');
    };

    return (
        <div className="min-h-screen bg-dark pt-20">
            {/* Hero Section */}
            <section className="py-12 sm:py-16">
                <div className="container mx-auto px-4">
                    <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.6 }}
                        className="text-center mb-12"
                    >
                        <h1 className="font-display text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold mb-4 text-gradient">
                            {t('hero.title')}
                        </h1>
                        <p className="text-xl sm:text-2xl text-primary mb-6 font-title">
                            {t('hero.subtitle')}
                        </p>
                        <p className="text-text-light text-lg max-w-3xl mx-auto leading-relaxed mb-8">
                            {t('hero.description')}
                        </p>
                        
                        {/* Features */}
                        <div className="flex flex-wrap justify-center gap-4 mb-8">
                            {Array.from({ length: 5 }, (_, i) => (
                                <div key={i} className="flex items-center gap-2 bg-dark-card/50 px-4 py-2 rounded-full border border-primary/20">
                                    <span className="text-primary">✓</span>
                                    <span className="text-sm text-text-light">{t(`hero.features.${i}`)}</span>
                                </div>
                            ))}
                        </div>
                    </motion.div>
                </div>
            </section>

            {/* Generator Section */}
            <section className="py-8">
                <div className="container mx-auto px-4">
                    <div className="max-w-4xl mx-auto">
                        {!generatedAvatar ? (
                            <motion.div 
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.6 }}
                                className="bg-dark-card/50 backdrop-blur-sm rounded-2xl p-8 border border-primary/20 relative"
                            >
                                {/* Loading Overlay */}
                                {isGenerating && (
                                    <div className="absolute inset-0 bg-dark-card/80 backdrop-blur-sm rounded-2xl flex items-center justify-center z-10">
                                        <div className="text-center space-y-4">
                                            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
                                            <p className="text-text-light font-title text-lg">
                                                Creating your avatar...
                                            </p>
                                            <p className="text-text-light/60 text-sm">
                                                This may take 10-30 seconds
                                            </p>
                                        </div>
                                    </div>
                                )}

                                <div className="space-y-6">
                                    {/* Prompt Input */}
                                    <div>
                                        <label className="block text-text-light font-title text-lg mb-3">
                                            {t('generator.form.promptLabel')}
                                        </label>
                                        <textarea
                                            value={prompt}
                                            onChange={(e) => setPrompt(e.target.value)}
                                            placeholder={t('generator.form.promptPlaceholder')}
                                            className="w-full bg-dark border border-primary/30 rounded-xl px-4 py-3 text-text-light placeholder-text-muted focus:border-primary focus:outline-none resize-none h-24"
                                            disabled={isGenerating}
                                        />
                                    </div>

                                    {/* Style Selection */}
                                    <div className="grid md:grid-cols-2 gap-6">
                                        <div>
                                            <label className="block text-text-light font-title text-lg mb-3">
                                                {t('generator.form.styleLabel')}
                                            </label>
                                            <select
                                                value={style}
                                                onChange={(e) => setStyle(e.target.value)}
                                                className="w-full bg-dark border border-primary/30 rounded-xl px-4 py-3 text-text-light focus:border-primary focus:outline-none select-dropdown"
                                                disabled={isGenerating}
                                            >
                                                {['photorealistic', 'artistic', 'illustration', 'abstract', 'fantasy'].map((s) => (
                                                    <option key={s} value={s}>
                                                        {t(`generator.form.styles.${s}`)}
                                                    </option>
                                                ))}
                                            </select>
                                        </div>

                                        {/* Aspect Ratio Selection */}
                                        <div>
                                            <label className="block text-text-light font-title text-lg mb-3">
                                                {t('generator.form.aspectRatioLabel')}
                                            </label>
                                            <select
                                                value={aspectRatio}
                                                onChange={(e) => setAspectRatio(e.target.value)}
                                                className="w-full bg-dark border border-primary/30 rounded-xl px-4 py-3 text-text-light focus:border-primary focus:outline-none select-dropdown"
                                                disabled={isGenerating}
                                            >
                                                {['square', 'portrait', 'landscape', 'wide'].map((ratio) => (
                                                    <option key={ratio} value={ratio}>
                                                        {t(`generator.form.aspectRatios.${ratio}`)}
                                                    </option>
                                                ))}
                                            </select>
                                        </div>
                                    </div>

                                    {/* Generate Button */}
                                    <button
                                        onClick={handleGenerate}
                                        disabled={isGenerating || !prompt.trim()}
                                        className="w-full btn-primary text-lg py-4 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
                                    >
                                        {isGenerating && (
                                            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                                        )}
                                        {isGenerating ? t('generator.form.generatingButton') : t('generator.form.generateButton')}
                                    </button>

                                    {/* Error Display */}
                                    {error && (
                                        <motion.div
                                            initial={{ opacity: 0, y: 10 }}
                                            animate={{ opacity: 1, y: 0 }}
                                            className="bg-red-500/10 border border-red-500/20 rounded-xl p-4 text-center"
                                        >
                                            <p className="text-red-400 font-medium">⚠️ {error}</p>
                                        </motion.div>
                                    )}
                                </div>
                            </motion.div>
                        ) : (
                            /* Result Display */
                            <motion.div
                                initial={{ opacity: 0, scale: 0.95 }}
                                animate={{ opacity: 1, scale: 1 }}
                                transition={{ duration: 0.5 }}
                                className="space-y-6"
                            >
                                <div className="text-center">
                                    <h2 className="text-2xl font-title font-bold text-gradient mb-4">
                                        {t('generator.result.title')}
                                    </h2>
                                </div>

                                {/* Avatar Display */}
                                <div className="relative max-w-md mx-auto">
                                    <div className="bg-gradient-to-br from-primary/20 to-secondary/20 rounded-2xl p-4 border border-primary/20">
                                        <div className="relative overflow-hidden rounded-xl">
                                            <img
                                                src={generatedAvatar.dataUrl || generatedAvatar.imageUrl}
                                                alt={`Generated avatar: ${generatedAvatar.prompt}`}
                                                className="w-full h-auto aspect-square object-cover rounded-xl"
                                                onError={(e) => {
                                                    const target = e.target as HTMLImageElement;
                                                    if (generatedAvatar.dataUrl && target.src !== generatedAvatar.dataUrl) {
                                                        target.src = generatedAvatar.dataUrl;
                                                    }
                                                }}
                                            />
                                            <div className="absolute bottom-2 right-2 bg-black/50 backdrop-blur-sm rounded-lg px-3 py-1">
                                                <p className="text-white text-xs">
                                                    {generatedAvatar.style}
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                {/* Action Buttons */}
                                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                                    <button
                                        onClick={handleDownload}
                                        className="btn-primary px-8 py-3 flex items-center justify-center gap-2"
                                    >
                                        <span>📥</span>
                                        {t('generator.result.downloadButton')}
                                    </button>
                                    <button
                                        onClick={handleGenerateAnother}
                                        className="btn-secondary px-8 py-3"
                                    >
                                        {t('generator.result.generateAnother')}
                                    </button>
                                </div>
                            </motion.div>
                        )}
                    </div>
                </div>
            </section>

            {/* Tips Section */}
            {!generatedAvatar && (
                <section className="py-8">
                    <div className="container mx-auto px-4">
                        <div className="max-w-4xl mx-auto">
                            <motion.div
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.6, delay: 0.2 }}
                                className="bg-dark-card/30 rounded-2xl p-6 border border-primary/10"
                            >
                                <h3 className="text-xl font-title font-bold text-gradient mb-4">
                                    {t('generator.tips.title')}
                                </h3>
                                <div className="grid md:grid-cols-2 gap-4">
                                    {Array.from({ length: 5 }, (_, i) => (
                                        <div key={i} className="flex items-start gap-3">
                                            <User className="w-4 h-4 text-primary mt-1 flex-shrink-0" />
                                            <p className="text-text-light text-sm">
                                                {t(`generator.tips.items.${i}`)}
                                            </p>
                                        </div>
                                    ))}
                                </div>
                            </motion.div>
                        </div>
                    </div>
                </section>
            )}

            {/* Features Section */}
            <section className="py-12 sm:py-16">
                <div className="container mx-auto px-4">
                    <div className="text-center mb-12">
                        <h2 className="font-display text-2xl sm:text-3xl md:text-4xl font-bold mb-4 text-gradient">
                            {t('features.title')}
                        </h2>
                        <p className="text-text-muted text-lg max-w-2xl mx-auto">
                            {t('features.subtitle')}
                        </p>
                    </div>

                    <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                        {['consistent', 'professional', 'versatile', 'instant', 'customizable', 'commercial'].map((feature, index) => (
                            <motion.div
                                key={feature}
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.6, delay: index * 0.1 }}
                                className="bg-dark-card/50 rounded-xl p-6 border border-primary/20 hover:border-primary/40 transition-all duration-300"
                            >
                                <div className="text-3xl mb-4">
                                    {feature === 'consistent' ? '🎯' :
                                     feature === 'professional' ? '💼' :
                                     feature === 'versatile' ? '🎨' :
                                     feature === 'instant' ? '⚡' :
                                     feature === 'customizable' ? '🛠️' : '✅'}
                                </div>
                                <h3 className="text-xl font-title font-bold text-gradient mb-3">
                                    {t(`features.items.${feature}.title`)}
                                </h3>
                                <p className="text-text-light leading-relaxed">
                                    {t(`features.items.${feature}.description`)}
                                </p>
                            </motion.div>
                        ))}
                    </div>
                </div>
            </section>

            {/* Avatar Prompt Examples Section */}
            <section className="py-12 sm:py-16 bg-dark-card/20">
                <div className="container mx-auto px-4">
                    <div className="text-center mb-12">
                        <h2 className="font-display text-2xl sm:text-3xl md:text-4xl font-bold mb-4 text-gradient">
                            {t('examples.title')}
                        </h2>
                        <p className="text-text-muted text-lg max-w-2xl mx-auto">
                            {t('examples.subtitle')}
                        </p>
                    </div>

                    <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                        {Array.from({ length: 6 }, (_, i) => (
                            <motion.div
                                key={i}
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.6, delay: i * 0.1 }}
                                className="bg-dark-card/50 rounded-xl p-4 sm:p-6 border border-primary/20 hover:border-primary/40 transition-all duration-300 cursor-pointer examples-card"
                                onClick={() => setPrompt(t(`examples.items.${i}.prompt`))}
                            >
                                <div className="mb-4">
                                    <div className="flex items-center gap-2 mb-3">
                                        <UserCircle className="w-5 h-5 text-primary" />
                                        <span className="text-xs font-semibold text-primary uppercase tracking-wide">
                                            {t(`examples.items.${i}.style`)}
                                        </span>
                                    </div>
                                    <h4 className="font-title font-semibold text-text-light mb-3 text-base sm:text-lg">
                                        {t(`examples.items.${i}.style`)} {t('examples.avatarExample')}
                                    </h4>
                                    <p className="text-text-light text-sm leading-relaxed mb-4 bg-dark/30 rounded-lg p-3 border-l-2 border-primary/30">
                                        &ldquo;{t(`examples.items.${i}.prompt`)}&rdquo;
                                    </p>
                                    <div className="text-xs text-text-muted">
                                        {t('examples.clickToTry')}
                                    </div>
                                </div>
                            </motion.div>
                        ))}
                    </div>
                </div>
            </section>

            {/* Use Cases Section */}
            <section className="py-12 sm:py-16">
                <div className="container mx-auto px-4">
                    <div className="text-center mb-12">
                        <h2 className="font-display text-2xl sm:text-3xl md:text-4xl font-bold mb-4 text-gradient">
                            {t('useCases.title')}
                        </h2>
                        <p className="text-text-muted text-lg max-w-2xl mx-auto">
                            {t('useCases.subtitle')}
                        </p>
                    </div>

                    <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                        {['social', 'gaming', 'business', 'content', 'marketing', 'personal'].map((useCase, index) => (
                            <motion.div
                                key={useCase}
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.6, delay: index * 0.1 }}
                                className="bg-dark-card/50 rounded-xl p-6 border border-primary/20 hover:border-primary/40 transition-all duration-300"
                            >
                                <div className="flex items-center justify-center w-12 h-12 rounded-lg bg-gradient-to-br from-primary/20 to-secondary/20 border border-primary/30 mb-4">
                                    {useCase === 'social' ? <Smartphone className="w-6 h-6 text-primary" /> :
                                     useCase === 'gaming' ? <Gamepad2 className="w-6 h-6 text-primary" /> :
                                     useCase === 'business' ? <Briefcase className="w-6 h-6 text-primary" /> :
                                     useCase === 'content' ? <Monitor className="w-6 h-6 text-primary" /> :
                                     useCase === 'marketing' ? <Megaphone className="w-6 h-6 text-primary" /> : <Palette className="w-6 h-6 text-primary" />}
                                </div>
                                <h3 className="text-xl font-title font-bold text-gradient mb-3">
                                    {t(`useCases.items.${useCase}.title`)}
                                </h3>
                                <p className="text-text-light leading-relaxed">
                                    {t(`useCases.items.${useCase}.description`)}
                                </p>
                            </motion.div>
                        ))}
                    </div>
                </div>
            </section>

            {/* FAQ Section */}
            <section className="py-12 sm:py-16 bg-dark-card/20">
                <div className="container mx-auto px-4">
                    <div className="text-center mb-12">
                        <h2 className="font-display text-2xl sm:text-3xl md:text-4xl font-bold mb-4 text-gradient">
                            {t('faq.title')}
                        </h2>
                    </div>

                    <div className="max-w-4xl mx-auto space-y-6">
                        {Array.from({ length: 5 }, (_, i) => (
                            <motion.div
                                key={i}
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.6, delay: i * 0.1 }}
                                className="bg-dark-card/50 rounded-xl p-6 border border-primary/20"
                            >
                                <h3 className="text-lg font-title font-semibold text-gradient mb-3">
                                    {t(`faq.items.${i + 1}.question`)}
                                </h3>
                                <p className="text-text-light leading-relaxed">
                                    {t(`faq.items.${i + 1}.answer`)}
                                </p>
                            </motion.div>
                        ))}
                    </div>
                </div>
            </section>
        </div>
    );
}
