import { Metadata } from 'next';
import { getTranslations, setRequestLocale } from 'next-intl/server';
import { notFound } from 'next/navigation';
import { routing } from '@/i18n/routing';
import AvatarCreatorContent from './AvatarCreatorContent';

type Props = {
    params: Promise<{ locale: string }>;
};

export async function generateMetadata({ params }: Props): Promise<Metadata> {
    const { locale } = await params;

    if (!routing.locales.includes(locale as any)) {
        notFound();
    }

    const t = await getTranslations({ locale, namespace: 'pages.avatarCreator.metadata' });

    return {
        title: t('title'),
        description: t('description'),
        keywords: t('keywords'),
        openGraph: {
            title: t('title'),
            description: t('description'),
            type: 'website',
            locale: locale,
            url: `https://a3i.ai/${locale}/avatar-creator`,
            siteName: 'a3i.ai',
            images: [
                {
                    url: 'https://a3i.ai/og-image.jpg',
                    width: 1200,
                    height: 630,
                    alt: t('title'),
                },
            ],
        },
        twitter: {
            card: 'summary_large_image',
            title: t('title'),
            description: t('description'),
            images: ['https://a3i.ai/og-image.jpg'],
        },
        alternates: {
            canonical: `https://a3i.ai/${locale}/avatar-creator`,
            languages: {
                'en': 'https://a3i.ai/en/avatar-creator',
                'zh': 'https://a3i.ai/zh/avatar-creator',
            },
        },
    };
}

export default async function AvatarCreatorPage({ params }: Props) {
    const { locale } = await params;

    if (!routing.locales.includes(locale as any)) {
        notFound();
    }

    setRequestLocale(locale);
    const t = await getTranslations('pages.avatarCreator.content');

    // Structured data for SEO
    const structuredData = {
        "@context": "https://schema.org",
        "@type": "WebApplication",
        "name": t('hero.title'),
        "description": t('hero.description'),
        "url": `https://a3i.ai/${locale}/avatar-creator`,
        "applicationCategory": "DesignApplication",
        "operatingSystem": "Web Browser",
        "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD"
        },
        "creator": {
            "@type": "Organization",
            "name": "a3i.ai",
            "url": "https://a3i.ai"
        }
    };

    return (
        <>
            <script
                type="application/ld+json"
                dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
            />
            <AvatarCreatorContent />
        </>
    );
}
