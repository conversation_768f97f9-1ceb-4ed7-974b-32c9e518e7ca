@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
    :root {
        --primary: #00BFFF;
        --primary-dark: #0099CC;
        --secondary: #A259FF;
        --accent: #FFD700;
        --background: #ffffff;
        --background-dark: #0F0F1A;
        --text: #333333;
        --text-light: #EAEAEA;
        --text-muted: #888888;

        /* Font variables */
        --font-sans: Inter, 'Noto Sans SC', system-ui, -apple-system, sans-serif;
        --font-display: 'Space Grotesk', Satoshi, Inter, sans-serif;
        --font-title: 'Plus Jakarta Sans', Sato<PERSON>, 'Noto Sans SC', sans-serif;
        --font-mono: 'JetBrains Mono', monospace;
    }

    html {
        @apply scroll-smooth;
        /* 确保100%高度 */
        height: 100%;
    }

    h1,
    h2,
    h3,
    h4 {
        @apply font-display tracking-tight;
    }

    h1 {
        @apply text-4xl md:text-5xl lg:text-6xl font-bold;
    }

    h2 {
        @apply text-3xl md:text-4xl font-bold;
    }

    h3 {
        @apply text-xl md:text-2xl font-semibold;
    }

    button,
    .button {
        @apply font-title font-medium;
    }
}

body {
    @apply bg-background-dark text-text-light min-h-screen;
    font-family: var(--font-sans);
    overflow-x: hidden;
    /* 确保body也有100%高度 */
    height: 100%;
    min-height: 100vh;
    min-height: 100dvh;
    /* 动态视窗高度，支持移动端 */
}

.text-gradient {
    @apply bg-clip-text text-transparent bg-gradient-primary;
    letter-spacing: 0.01em;
}

.text-gradient-secondary {
    @apply bg-clip-text text-transparent bg-gradient-secondary;
    letter-spacing: 0.025em;
}

.text-gradient-accent {
    @apply bg-clip-text text-transparent bg-gradient-accent;
    letter-spacing: 0.02em;
}

.font-display {
    font-family: var(--font-display);
}

.font-title {
    font-family: var(--font-title);
}

.font-mono {
    font-family: var(--font-mono);
}

/* 更精细的字母间距控制 */
.hero-title {
    @apply text-gradient;
    letter-spacing: 0.02em;
}

.hero-subtitle {
    @apply text-gradient-secondary;
    letter-spacing: 0.035em;
}

/* Hero标题的响应式行高控制 */
@media (max-width: 767px) {

    .hero-title,
    .hero-subtitle {
        line-height: 1.1;
        display: block;
        margin-bottom: 0.15em;
    }

    .hero-subtitle {
        margin-bottom: 0;
        margin-top: 0.1em;
    }

    /* 移动端优化的字体大小 */
    .hero-title {
        font-size: clamp(1.5rem, 7vw, 2.5rem) !important;
        line-height: 1.1 !important;
    }

    .hero-subtitle {
        font-size: clamp(1.25rem, 6vw, 2rem) !important;
        line-height: 1.1 !important;
        margin-top: 0.25rem !important;
    }
}

@media (min-width: 768px) and (max-width: 1023px) {

    .hero-title,
    .hero-subtitle {
        line-height: 0.95;
    }

    .hero-subtitle {
        margin-left: 0.3em;
    }
}

@media (min-width: 1024px) {

    .hero-title,
    .hero-subtitle {
        line-height: 0.9;
    }
}

.section-title {
    letter-spacing: 0.04em;
}

.card-title {
    letter-spacing: 0.02em;
}

.button-text {
    letter-spacing: 0.04em;
}

.nav-text {
    letter-spacing: 0.025em;
}

@layer utilities {
    .bg-gradient-soft {
        @apply bg-gradient-to-b from-dark-lighter to-dark;
    }

    .bg-gradient-card {
        @apply bg-gradient-to-br from-dark-card to-dark;
    }

    .hover-card {
        @apply transition-all duration-300;
    }

    /* 使用直接CSS定义而不是@apply动态类 */
    .hover-card:hover {
        box-shadow: 0 0 15px rgba(0, 191, 255, 0.3);
    }

    .glow {
        box-shadow: 0 0 20px rgba(162, 89, 255, 0.4);
    }

    .glow-primary {
        box-shadow: 0 0 20px rgba(0, 191, 255, 0.4);
    }

    .glow-accent {
        box-shadow: 0 0 20px rgba(255, 215, 0, 0.4);
    }

    /* 输入框按钮渐变 - 修复透明度问题 */
    .bg-generate-button {
        background: linear-gradient(90deg, #2C91F9 0%, #7C66FF 100%);
    }

    .tracking-tighter {
        letter-spacing: -0.05em;
    }

    .tracking-tight {
        letter-spacing: -0.025em;
    }

    .tracking-wide {
        letter-spacing: 0.025em;
    }

    .tracking-wider {
        letter-spacing: 0.04em;
    }

    .tracking-widest {
        letter-spacing: 0.06em;
    }

    /* 页面内容容器优化 */
    .content-container {
        @apply pt-16 sm:pt-20;
        min-height: 100vh;
    }

    /* Hero Section背景图片和渐变效果 */
    .hero-image-wrapper {
        @apply overflow-hidden relative;
    }

    .hero-image-wrapper::after {
        content: '';
        @apply absolute inset-0 bg-gradient-to-b from-background-dark/60 via-transparent to-background-dark/40 z-10;
    }

    .hero-glass-effect {
        backdrop-filter: blur(8px);
        @apply bg-dark-card/40 border border-primary/10 rounded-xl;
    }

    /* Hero内容区域布局优化 */
    .hero-content {
        /* 移动端优化：考虑Header高度和安全区域 */
        padding-top: calc(env(safe-area-inset-top) + 4rem);
        padding-bottom: calc(env(safe-area-inset-bottom) + 2rem);
    }

    /* 响应式Hero section padding调整 */
    @media screen and (max-height: 700px) {
        section[class*="min-h-screen"] .container {
            @apply py-8;
        }

        .hero-content {
            padding-top: calc(env(safe-area-inset-top) + 2rem);
            padding-bottom: calc(env(safe-area-inset-bottom) + 1rem);
        }
    }

    @media screen and (max-height: 600px) {
        section[class*="min-h-screen"] .container {
            @apply py-4;
        }

        .hero-content {
            padding-top: calc(env(safe-area-inset-top) + 1rem);
            padding-bottom: calc(env(safe-area-inset-bottom) + 0.5rem);
        }
    }

    /* 移动端优化 */
    @media (max-width: 640px) {
        section[class*="min-h-screen"] .container {
            @apply py-0;
        }

        .hero-content {
            padding-top: calc(env(safe-area-inset-top) + 5rem);
            padding-bottom: calc(env(safe-area-inset-bottom) + 3rem);
            min-height: calc(100vh - env(safe-area-inset-top) - env(safe-area-inset-bottom));
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        /* 移动端标题优化 */
        .hero-title {
            font-size: clamp(1.5rem, 6vw, 2.5rem);
            line-height: 1.1;
        }

        .hero-subtitle {
            font-size: clamp(1.25rem, 5vw, 2rem);
            line-height: 1.1;
            margin-top: 0.25rem;
        }
    }

    /* 平板优化 */
    @media (min-width: 641px) and (max-width: 1024px) {
        section[class*="min-h-screen"] .container {
            @apply py-12;
        }

        .hero-content {
            padding-top: calc(env(safe-area-inset-top) + 3rem);
            padding-bottom: calc(env(safe-area-inset-bottom) + 2rem);
        }
    }

    /* 桌面端优化 */
    @media (min-width: 1025px) {
        .hero-content {
            padding-top: calc(env(safe-area-inset-top) + 2rem);
            padding-bottom: calc(env(safe-area-inset-bottom) + 2rem);
        }
    }

    /* 超大屏幕优化 */
    @media (min-width: 1920px) {
        section[class*="min-h-screen"] .container {
            @apply py-16;
        }

        .hero-content {
            padding-top: calc(env(safe-area-inset-top) + 1rem);
            padding-bottom: calc(env(safe-area-inset-bottom) + 1rem);
        }
    }

    /* 横屏手机优化 */
    @media (max-width: 896px) and (max-height: 414px) and (orientation: landscape) {
        section[class*="min-h-screen"] .container {
            @apply py-2;
        }

        .hero-content {
            padding-top: calc(env(safe-area-inset-top) + 1rem);
            padding-bottom: calc(env(safe-area-inset-bottom) + 0.5rem);
        }

        section[class*="min-h-screen"] h1 {
            @apply text-lg;
        }

        section[class*="min-h-screen"] h2 {
            @apply text-base;
        }

        /* 横屏时减少间距 */
        .hero-content>div:not(:last-child) {
            margin-bottom: 1rem !important;
        }
    }

    /* iPhone X/11/12/13/14 系列适配 */
    @media only screen and (device-width: 375px) and (device-height: 812px) and (-webkit-device-pixel-ratio: 3) {
        .hero-content {
            padding-top: calc(env(safe-area-inset-top) + 4.5rem);
        }
    }

    /* iPhone 14 Pro Max 适配 */
    @media only screen and (device-width: 430px) and (device-height: 932px) and (-webkit-device-pixel-ratio: 3) {
        .hero-content {
            padding-top: calc(env(safe-area-inset-top) + 4.5rem);
        }
    }

    /* 星空动画效果 */
    .star {
        @apply absolute rounded-full bg-white;
        box-shadow: 0 0 3px rgba(255, 255, 255, 0.8);
        animation: twinkle var(--twinkle-duration) ease-in-out infinite;
        opacity: 0.5;
    }

    .star-1 {
        width: 2px;
        height: 2px;
        --twinkle-duration: 4s;
    }

    .star-2 {
        width: 3px;
        height: 3px;
        --twinkle-duration: 6s;
    }

    .star-3 {
        width: 1px;
        height: 1px;
        --twinkle-duration: 3s;
    }

    @keyframes twinkle {

        0%,
        100% {
            opacity: 0.3;
            transform: scale(0.8);
            box-shadow: 0 0 2px rgba(255, 255, 255, 0.3);
        }

        50% {
            opacity: 1;
            transform: scale(1.3);
            box-shadow: 0 0 8px 2px rgba(255, 255, 255, 0.9), 0 0 12px 4px rgba(0, 191, 255, 0.4);
        }
    }

    /* 流星动画 */
    .shooting-star {
        @apply absolute h-[1.5px] z-10;
        background: linear-gradient(90deg, transparent, #fff 20%, rgba(255, 255, 255, 0.8));
        transform: rotate(45deg);
        filter: drop-shadow(0 0 6px rgba(255, 255, 255, 0.9));
        animation: shooting var(--shooting-duration) ease-out infinite;
        animation-delay: var(--shooting-delay);
        opacity: 0;
    }

    .shooting-star::before {
        content: '';
        @apply absolute h-full w-[60px] bg-gradient-to-r;
        background-image: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.8) 40%, rgba(0, 191, 255, 0.4) 80%, transparent);
        transform: translateX(-30px);
    }

    @keyframes shooting {
        0% {
            opacity: 0;
            transform: translateX(0) translateY(0) rotate(45deg);
        }

        3% {
            opacity: 0.6;
        }

        6% {
            opacity: 1;
            transform: translateX(calc(var(--shooting-distance) * 0.1)) translateY(calc(var(--shooting-distance) * 0.1)) rotate(45deg);
        }

        20% {
            transform: translateX(calc(var(--shooting-distance) * 0.3)) translateY(calc(var(--shooting-distance) * 0.3)) rotate(45deg);
            opacity: 0;
        }

        100% {
            transform: translateX(var(--shooting-distance)) translateY(var(--shooting-distance)) rotate(45deg);
            opacity: 0;
        }
    }

    /* iOS Safari 视窗高度修复 */
    @supports (-webkit-touch-callout: none) {
        body {
            min-height: -webkit-fill-available;
        }

        section[class*="min-h-screen"] {
            min-height: -webkit-fill-available;
        }
    }

    /* 现代浏览器动态视窗高度支持 */
    @supports (height: 100dvh) {
        section[class*="min-h-screen"] {
            min-height: 100dvh;
        }
    }

    /* 滚动指示器动画优化 */
    .animate-bounce {
        animation: bounce 2s infinite;
    }

    @keyframes bounce {

        0%,
        20%,
        53%,
        80%,
        100% {
            transform: translate(-50%, 0);
        }

        40%,
        43% {
            transform: translate(-50%, -10px);
        }

        70% {
            transform: translate(-50%, -5px);
        }

        90% {
            transform: translate(-50%, -2px);
        }
    }

    /* 通用的Hero区域优化 */
    section[class*="min-h-screen"] {
        position: relative;
        overflow: hidden;
    }

    /* 确保Hero内容垂直居中 */
    section[class*="min-h-screen"]>div {
        display: flex;
        align-items: center;
        justify-content: center;
    }

    /* 移动端交互元素优化 */
    @media (max-width: 640px) {

        /* 输入框移动端优化 */
        input[placeholder*="vision"] {
            font-size: 16px !important;
            /* 防止iOS自动缩放 */
            min-height: 44px;
            /* iOS最小触摸区域 */
        }

        button[class*="bg-generate-button"] {
            min-height: 44px;
            /* iOS最小触摸区域 */
            font-size: 14px !important;
        }

        /* 移动端Brand区域优化 */
        .hero-content>div:first-child {
            margin-bottom: 1.5rem !important;
        }

        /* 移动端描述文字优化 */
        .hero-content p {
            font-size: clamp(0.875rem, 4vw, 1rem) !important;
            line-height: 1.5 !important;
            margin-bottom: 2rem !important;
        }
    }

    /* 平板端优化 */
    @media (min-width: 641px) and (max-width: 1024px) {
        .hero-content>div:first-child {
            margin-bottom: 2rem !important;
        }

        .hero-content p {
            font-size: clamp(1rem, 2.5vw, 1.25rem) !important;
            line-height: 1.6 !important;
        }
    }

    /* 桌面端优化 */
    @media (min-width: 1025px) {
        .hero-content {
            max-width: 1200px;
            margin: 0 auto;
        }
    }

    /* 超宽屏优化 */
    @media (min-width: 1440px) {
        .hero-content {
            max-width: 1400px;
        }

        .hero-content h1 {
            font-size: clamp(3rem, 4vw, 4rem) !important;
        }

        .hero-content h2 {
            font-size: clamp(2.5rem, 5vw, 5rem) !important;
        }
    }

    /* 提升可访问性 */
    @media (prefers-reduced-motion: reduce) {

        .animate-bounce,
        .animate-pulse,
        .star,
        .shooting-star {
            animation: none !important;
        }
    }

    /* 高对比度模式优化 */
    @media (prefers-contrast: high) {
        .hero-content {
            background: rgba(0, 0, 0, 0.8);
            border-radius: 1rem;
            padding: 2rem;
        }

        .text-gradient,
        .text-gradient-secondary {
            color: #ffffff !important;
            background: none !important;
            -webkit-background-clip: initial !important;
            background-clip: initial !important;
        }
    }

    /* FAQ Section 样式优化 */
    details {
        transition: all 0.3s ease;
        border: 1px solid rgba(0, 191, 255, 0.1);
    }

    details[open] {
        background: linear-gradient(135deg, rgba(0, 191, 255, 0.05), rgba(162, 89, 255, 0.05));
        border-color: rgba(0, 191, 255, 0.2);
    }

    details summary {
        transition: all 0.3s ease;
        border-radius: 0.75rem;
    }

    details[open] summary {
        border-bottom: none;
        border-radius: 0.75rem 0.75rem 0 0;
        margin-bottom: 0;
    }

    details summary:hover {
        background: rgba(0, 191, 255, 0.05);
    }

    details summary::-webkit-details-marker {
        display: none;
    }

    details summary::marker {
        display: none;
    }

    /* 修复focus状态的outline问题 */
    details summary:focus {
        outline: none;
        background: rgba(0, 191, 255, 0.08);
        box-shadow: 0 0 0 2px rgba(0, 191, 255, 0.3);
    }

    details summary:focus-visible {
        outline: none;
        background: rgba(0, 191, 255, 0.08);
        box-shadow: 0 0 0 2px rgba(0, 191, 255, 0.3);
    }

    /* FAQ内容区域优化 */
    .faq-content {
        border-top: 1px solid rgba(0, 191, 255, 0.1);
        margin-top: 0;
        background: rgba(0, 0, 0, 0.2);
        border-radius: 0 0 0.75rem 0.75rem;
    }

    details[open] .faq-content {
        animation: fadeInContent 0.3s ease forwards;
    }

    .faq-content>div {
        border-top: none;
        margin: 0;
        background: transparent;
    }

    @keyframes fadeInContent {
        from {
            opacity: 0;
            transform: translateY(-10px);
        }

        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* 响应式FAQ优化 */
    @media (max-width: 640px) {
        details {
            border-radius: 0.75rem;
        }

        details summary {
            padding: 1rem;
            border-radius: 0.75rem;
        }

        details[open] summary {
            border-radius: 0.75rem 0.75rem 0 0;
        }

        .faq-content>div {
            padding: 1rem;
            padding-top: 0.75rem;
        }

        details h3 {
            font-size: 0.9rem;
            line-height: 1.4;
        }

        details p {
            font-size: 0.875rem;
            line-height: 1.6;
        }

        /* 移动端focus优化 */
        details summary:focus,
        details summary:focus-visible {
            box-shadow: 0 0 0 1px rgba(0, 191, 255, 0.3);
        }
    }

    /* 桌面端hover和focus状态增强 */
    @media (min-width: 641px) {
        details summary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 191, 255, 0.15);
        }

        details summary:focus:hover {
            box-shadow: 0 0 0 2px rgba(0, 191, 255, 0.3), 0 4px 12px rgba(0, 191, 255, 0.15);
        }
    }

    details[open] summary h3 {
        color: rgb(0, 191, 255);
    }

    /* 提升可访问性 */
    @media (prefers-reduced-motion: reduce) {

        details,
        details summary,
        details svg,
        details div {
            transition: none !important;
            animation: none !important;
        }
    }

    /* 统一下拉选择框样式 */
    .select-dropdown {
        appearance: none;
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%2300BFFF' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
        background-position: right 12px center;
        background-repeat: no-repeat;
        background-size: 16px;
        padding-right: 40px;
    }

    .select-dropdown:focus {
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%2300BFFF' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
    }

    .select-dropdown:disabled {
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23888888' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
        opacity: 0.6;
    }

    /* 响应式优化 */
    @media (max-width: 640px) {
        .select-dropdown {
            background-position: right 10px center;
            background-size: 14px;
            padding-right: 36px;
            font-size: 0.875rem; /* 14px */
        }
    }

    @media (min-width: 641px) and (max-width: 768px) {
        .select-dropdown {
            background-position: right 11px center;
            background-size: 15px;
            padding-right: 38px;
        }
    }

    /* 确保在所有设备上都有足够的触摸目标大小 */
    @media (max-width: 768px) {
        .select-dropdown {
            min-height: 44px; /* iOS推荐的最小触摸目标 */
        }

        /* 移动端优化：减少examples卡片的padding */
        .examples-card {
            padding: 1rem !important;
        }

        /* 移动端优化：调整文字大小 */
        .examples-card h4 {
            font-size: 1rem !important;
        }

        .examples-card .text-xs {
            font-size: 0.75rem !important;
        }
    }

    /* 高对比度模式FAQ优化 */
    @media (prefers-contrast: high) {
        details {
            border: 2px solid #ffffff;
            background: #000000;
        }

        details[open] {
            background: #000000;
            border-color: rgb(0, 191, 255);
        }

        details div {
            border-top-color: #ffffff;
        }

        details summary h3 {
            color: #ffffff;
        }

        details[open] summary h3 {
            color: rgb(0, 191, 255);
        }

        details summary:focus,
        details summary:focus-visible {
            outline: 2px solid #ffffff;
            box-shadow: none;
        }
    }
}