import { prisma } from './prisma'

export interface CreditTransaction {
  userId: string
  type: 'PURCHASE' | 'USAGE' | 'BONUS' | 'REFUND'
  amount: number
  description?: string
  reference?: string
}

export class CreditsService {
  /**
   * 获取用户当前积分余额
   */
  static async getUserCredits(userId: string): Promise<number> {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { credits: true }
    })
    return user?.credits || 0
  }

  /**
   * 扣除用户积分（用于AI生成等操作）
   */
  static async deductCredits(
    userId: string, 
    amount: number, 
    description: string,
    reference?: string
  ): Promise<{ success: boolean; newBalance: number; error?: string }> {
    try {
      const result = await prisma.$transaction(async (tx) => {
        // 获取当前用户积分
        const user = await tx.user.findUnique({
          where: { id: userId },
          select: { credits: true }
        })

        if (!user) {
          throw new Error('User not found')
        }

        // 检查余额是否足够
        if (user.credits < amount) {
          throw new Error('Insufficient credits')
        }

        // 更新用户积分
        const updatedUser = await tx.user.update({
          where: { id: userId },
          data: { credits: user.credits - amount },
          select: { credits: true }
        })

        // 创建交易记录
        await tx.creditTransaction.create({
          data: {
            userId,
            type: 'USAGE',
            amount: -amount,
            description,
            reference
          }
        })

        return updatedUser.credits
      })

      return { success: true, newBalance: result }
    } catch (error) {
      return { 
        success: false, 
        newBalance: 0, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      }
    }
  }

  /**
   * 添加用户积分（用于购买、奖励等）
   */
  static async addCredits(
    userId: string,
    amount: number,
    type: 'PURCHASE' | 'BONUS' | 'REFUND',
    description: string,
    reference?: string
  ): Promise<{ success: boolean; newBalance: number; error?: string }> {
    try {
      const result = await prisma.$transaction(async (tx) => {
        // 更新用户积分
        const updatedUser = await tx.user.update({
          where: { id: userId },
          data: { credits: { increment: amount } },
          select: { credits: true }
        })

        // 创建交易记录
        await tx.creditTransaction.create({
          data: {
            userId,
            type,
            amount,
            description,
            reference
          }
        })

        return updatedUser.credits
      })

      return { success: true, newBalance: result }
    } catch (error) {
      return { 
        success: false, 
        newBalance: 0, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      }
    }
  }

  /**
   * 获取用户交易历史
   */
  static async getUserTransactions(
    userId: string,
    limit: number = 20,
    offset: number = 0
  ) {
    const transactions = await prisma.creditTransaction.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' },
      take: limit,
      skip: offset,
      select: {
        id: true,
        type: true,
        amount: true,
        description: true,
        reference: true,
        createdAt: true
      }
    })

    const total = await prisma.creditTransaction.count({
      where: { userId }
    })

    return {
      transactions,
      total,
      hasMore: offset + limit < total
    }
  }

  /**
   * 检查用户是否有足够的积分
   */
  static async hasEnoughCredits(userId: string, requiredAmount: number): Promise<boolean> {
    const currentCredits = await this.getUserCredits(userId)
    return currentCredits >= requiredAmount
  }
}

// 常量定义
export const CREDIT_COSTS = {
  IMAGE_GENERATION: 1,
  AVATAR_CREATION: 1,
  IMAGE_TO_TEXT: 1,
  VIDEO_GENERATION: 3, // 视频生成消耗更多积分
} as const

export const CREDIT_PLANS = {
  starter: {
    credits: 100,
    monthlyPrice: 9.99,
    yearlyPrice: 95.99,
  },
  pro: {
    credits: 500,
    monthlyPrice: 29.99,
    yearlyPrice: 287.99,
  },
  premium: {
    credits: 1500,
    monthlyPrice: 79.99,
    yearlyPrice: 767.99,
  },
} as const
