import axios from 'axios';

const FEISHU_WEBHOOK_URL = process.env.NEXT_PUBLIC_FEISHU_NOTIFY_WEBHOOK_URL;
const FEISHU_KEY_WEBHOOK_URL = process.env.NEXT_PUBLIC_FEISHU_KEY_WEBHOOK_URL;

async function sendFeishuNotification(webhookUrl: string | undefined, message: string) {
    // 如果未配置 webhook URL，静默返回
    if (!webhookUrl) {
        return;
    }

    try {
        await axios.post(webhookUrl, {
            msg_type: 'text',
            content: { text: message },
        });
    } catch (error) {
        // 在开发环境下才显示错误信息
        if (process.env.NODE_ENV === 'development') {
            console.error('飞书通知发送失败:', error);
        }
    }
}

export const notifyFeishu = {
    notify: (message: string) => sendFeishuNotification(FEISHU_WEBHOOK_URL, message),
    keyNotify: (message: string) => sendFeishuNotification(FEISHU_KEY_WEBHOOK_URL, message),
};