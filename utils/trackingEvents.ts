export enum TrackingEvents {
    PAGE_VIEW = 'page_view',
    NAVIGATION_CLICK = 'navigation_click',
    LANGUAGE_CHANGE = 'language_change',
    MOBILE_MENU_TOGGLE = 'mobile_menu_toggle',
    DROPDOWN_OPEN = 'dropdown_open'
}

export enum TrackingCategories {
    NAVIGATION = 'navigation',
    USER_INTERACTION = 'user_interaction',
    LANGUAGE = 'language',
    SKILLS = 'skills'
}

export type TrackingEvent = typeof TrackingEvents[keyof typeof TrackingEvents];
export type TrackingCategory = typeof TrackingCategories[keyof typeof TrackingCategories]; 