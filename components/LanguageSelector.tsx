'use client'

import { useState, useRef, useEffect } from 'react';
import { useRouter, usePathname, locales, fullLanguageNames, Pathnames } from '@/i18n/routing';
import { useLocale } from 'next-intl';

type Locale = keyof typeof fullLanguageNames;

const languageLabels: Record<string, string> = {
    'en': 'EN',
    'zh': '中',
};

interface LanguageSelectorProps {
    onLanguageChange?: (lang: string) => void;
    footerStyle?: boolean;
}

export default function LanguageSelector({ onLanguageChange, footerStyle = false }: LanguageSelectorProps) {
    const [isOpen, setIsOpen] = useState(false);
    const currentLocale = useLocale() as Locale;
    const pathname = usePathname();
    const router = useRouter();
    const dropdownRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
                setIsOpen(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);

    const handleLanguageChange = async (newLocale: string) => {
        onLanguageChange?.(newLocale);
        try {
            await router.replace(pathname as any, { locale: newLocale });
            router.refresh();
            setIsOpen(false);
        } catch (error) {
            console.error('Error changing language:', error);
        }
    };

    return (
        <div className={`relative ${footerStyle ? "inline-block" : ""}`} ref={dropdownRef}>
            <button
                onClick={() => setIsOpen(!isOpen)}
                className={`flex items-center transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-primary/50 ${footerStyle
                    ? "text-text-muted hover:text-primary space-x-2"
                    : "p-1.5 sm:p-2 rounded-lg border border-primary/30 hover:border-primary/60 bg-dark-card/80 hover:glow-primary justify-center"
                    }`}
                aria-label={`Select language: current language is ${fullLanguageNames[currentLocale]}`}
                aria-haspopup="true"
                aria-expanded={isOpen}
                lang={currentLocale}
            >
                <span className={`${footerStyle ? "text-base" : "text-lg sm:text-xl"} font-semibold text-primary`}>{languageLabels[currentLocale]}</span>
                <span className={`font-title nav-text ${footerStyle ? "text-xs sm:text-sm" : "hidden md:inline ml-2 text-sm font-medium text-text-light"}`}>
                    {fullLanguageNames[currentLocale]}
                </span>
                {!footerStyle && (
                    <svg
                        className="hidden md:inline w-4 h-4 ml-2 text-primary/70"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                        aria-hidden="true"
                    >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                )}
            </button>
            {isOpen && (
                <div
                    className={`bg-dark-card border border-primary/20 rounded-lg shadow-lg py-1 z-10 glow-primary ${footerStyle
                        ? "absolute bottom-full mb-2 left-0 w-48"
                        : "absolute right-0 mt-2 w-48"
                        }`}
                    role="menu"
                    aria-labelledby="language-menu"
                >
                    <div className="max-h-48 overflow-y-auto">
                        {locales.map((locale) => (
                            <button
                                key={locale}
                                onClick={() => handleLanguageChange(locale)}
                                className={`flex items-center space-x-2 w-full text-left px-4 py-2 text-sm hover:bg-dark-lighter transition-colors font-title nav-text
                                    ${currentLocale === locale ? 'text-primary' : 'text-text-light'}`}
                                lang={locale}
                                aria-current={currentLocale === locale ? "true" : "false"}
                                role="menuitem"
                                title={`Switch language to ${fullLanguageNames[locale]}`}
                            >
                                <span className="text-lg font-semibold text-primary">{languageLabels[locale]}</span>
                                <span className="text-xs sm:text-sm tracking-wide">{fullLanguageNames[locale]}</span>
                            </button>
                        ))}
                    </div>
                </div>
            )}
        </div>
    );
}