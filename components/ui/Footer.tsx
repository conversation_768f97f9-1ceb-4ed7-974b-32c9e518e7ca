import { useTranslations } from 'next-intl';
import { Link, Pathnames } from '@/i18n/routing';
import LanguageSelector from '@/components/LanguageSelector';
import Image from 'next/image';

export default function Footer() {
    const t = useTranslations('components.footer');

    return (
        <footer className="w-full bg-dark-lighter/30 border-t border-primary/10">
            <div className="container mx-auto px-4 py-6 sm:py-8">
                <div className="grid grid-cols-2 md:grid-cols-4 gap-5 sm:gap-6 mb-6">
                    <div className="col-span-2 md:col-span-1 mb-4 md:mb-0">
                        <div className="flex items-center space-x-3 sm:space-x-4 mb-4 sm:mb-5">
                            <div className="relative w-8 h-8 sm:w-10 sm:h-10 flex-shrink-0">
                                <Image
                                    src="/logo.webp"
                                    alt="a3i.ai Logo"
                                    fill
                                    className="object-contain"
                                    sizes="(max-width: 640px) 32px, 40px"
                                />
                            </div>
                            <div className="flex flex-col justify-center">
                                <h3 className="text-lg sm:text-xl font-display font-bold text-gradient tracking-tight leading-none">
                                    a3i.ai
                                </h3>
                                <span className="text-[10px] sm:text-xs font-title text-text-muted tracking-wide leading-none mt-0.5 opacity-75">
                                    Create Beyond Imagination.
                                </span>
                            </div>
                        </div>
                        <p className="text-text-muted text-xs sm:text-sm mb-4 sm:mb-5 font-sans tracking-wide leading-relaxed">
                            {t('tagline')}
                        </p>
                        <div className="flex space-x-4">
                            <a href="https://twitter.com" target="_blank" rel="noopener noreferrer" className="text-text-muted hover:text-primary font-title nav-text text-xs sm:text-sm transition-colors">
                                Twitter
                            </a>
                            <a href="https://discord.com" target="_blank" rel="noopener noreferrer" className="text-text-muted hover:text-primary font-title nav-text text-xs sm:text-sm transition-colors">
                                Discord
                            </a>
                        </div>
                    </div>
                    <div>
                        <h3 className="text-sm sm:text-base font-display font-semibold text-text-light mb-2 sm:mb-3 tracking-tight section-title">{t('links.quickLinks')}</h3>
                        <ul className="space-y-1 sm:space-y-2 font-title">
                            <li>
                                <Link href={"/" as Pathnames} className="text-text-muted hover:text-primary transition-colors nav-text text-xs sm:text-sm">
                                    {t('links.home')}
                                </Link>
                            </li>
                            <li>
                                <Link href={"/image-generator" as Pathnames} className="text-text-muted hover:text-primary transition-colors nav-text text-xs sm:text-sm">
                                    Image Generator
                                </Link>
                            </li>
                            <li>
                                <Link href={"/avatar-creator" as Pathnames} className="text-text-muted hover:text-primary transition-colors nav-text text-xs sm:text-sm">
                                    Avatar Creator
                                </Link>
                            </li>
                            <li>
                                <Link href={"/" as Pathnames} className="text-text-muted hover:text-primary transition-colors nav-text text-xs sm:text-sm">
                                    {t('links.gallery')}
                                </Link>
                            </li>
                        </ul>
                    </div>
                    <div>
                        <h3 className="text-sm sm:text-base font-display font-semibold text-text-light mb-2 sm:mb-3 tracking-tight section-title">{t('links.legal')}</h3>
                        <ul className="space-y-1 sm:space-y-2 font-title">
                            <li>
                                <Link href={"/privacy-policy" as Pathnames} className="text-text-muted hover:text-primary transition-colors nav-text text-xs sm:text-sm">
                                    {t('links.privacyPolicy')}
                                </Link>
                            </li>
                            <li>
                                <Link href={"/terms-of-service" as Pathnames} className="text-text-muted hover:text-primary transition-colors nav-text text-xs sm:text-sm">
                                    {t('links.termsOfService')}
                                </Link>
                            </li>
                        </ul>
                    </div>
                    <div>
                        <h3 className="text-sm sm:text-base font-display font-semibold text-text-light mb-2 sm:mb-3 tracking-tight section-title">{t('links.language')}</h3>
                        <p className="text-xs sm:text-sm text-text-muted mb-2 font-sans tracking-wide">
                            {t('language.chooseLang')}
                        </p>
                        <LanguageSelector footerStyle />
                    </div>
                </div>
                <div className="border-t border-primary/10 pt-4 sm:pt-6 flex flex-col sm:flex-row justify-between items-center">
                    <p className="text-xs sm:text-sm text-text-muted font-sans tracking-wide mb-2 sm:mb-0">
                        {t('copyright')}
                    </p>
                    <div className="flex items-center space-x-3 text-xs text-text-muted">
                        <span>{t('siteOptimized')}</span>
                    </div>
                </div>
            </div>
        </footer>
    );
}