'use client'

import { Link, Pathnames } from '@/i18n/routing';
import { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import { useAuth } from '@/hooks/useAuth';
import { useCredits } from '@/hooks/useCredits';
import { User, LogIn, LogOut, Coins, CreditCard, Settings } from 'lucide-react';

export default function Header() {
    const [isVisible, setIsVisible] = useState(false);
    const [isMenuOpen, setIsMenuOpen] = useState(false);
    const [isToolsDropdownOpen, setIsToolsDropdownOpen] = useState(false);
    const [isUserDropdownOpen, setIsUserDropdownOpen] = useState(false);
    const t = useTranslations('site');
    const { user, isAuthenticated, isLoading, signIn, signOut } = useAuth();
    const { credits, loading: creditsLoading } = useCredits();

    useEffect(() => {
        const timer = setTimeout(() => {
            setIsVisible(true);
        }, 500);

        return () => {
            clearTimeout(timer);
        };
    }, []);

    return (
        <AnimatePresence>
            {isVisible && (
                <motion.header
                    initial={{ y: -100, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{
                        duration: 0.5,
                        ease: "easeOut"
                    }}
                    className="fixed top-0 left-0 right-0 z-50 backdrop-blur-lg bg-dark-card/80 border-b border-primary/20"
                >
                    <div className="container mx-auto px-4 py-3 sm:py-4">
                        <nav className="flex justify-between items-center min-h-[48px] sm:min-h-[56px]">
                            {/* Logo, Site Title and Slogan */}
                            <Link href={"/" as Pathnames} className="flex items-center space-x-3 sm:space-x-4 group py-1">
                                <div className="relative w-10 h-10 sm:w-12 sm:h-12 flex-shrink-0">
                                    <Image
                                        src="/logo.webp"
                                        alt="a3i.ai Logo"
                                        fill
                                        className="object-contain transition-transform duration-300 group-hover:scale-110"
                                        priority
                                        sizes="(max-width: 640px) 40px, 48px"
                                    />
                                </div>
                                <div className="flex flex-col justify-center">
                                    <span className="text-xl sm:text-2xl font-display font-bold tracking-tighter text-gradient leading-none">
                                        {t('title')}
                                    </span>
                                    <span className="text-[10px] sm:text-xs font-title text-text-muted tracking-wide leading-none mt-0.5 opacity-75 group-hover:opacity-90 transition-opacity duration-300">
                                        Create Beyond Imagination.
                                    </span>
                                </div>
                            </Link>

                            {/* Mobile Menu Button */}
                            <button
                                className="block sm:hidden text-text-light hover:text-primary p-2 rounded-lg transition-colors"
                                onClick={() => setIsMenuOpen(!isMenuOpen)}
                                aria-label="Toggle menu"
                            >
                                {isMenuOpen ? (
                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                ) : (
                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                                    </svg>
                                )}
                            </button>

                            {/* Desktop Menu */}
                            <div className="hidden sm:flex items-center space-x-6">
                                {/* Tools Dropdown */}
                                <div className="relative">
                                    <button
                                        className="text-text-light hover:text-primary transition-colors font-title nav-text flex items-center gap-2"
                                        onClick={() => setIsToolsDropdownOpen(!isToolsDropdownOpen)}
                                        onBlur={() => setTimeout(() => setIsToolsDropdownOpen(false), 150)}
                                    >
                                        {t('nav.tools')}
                                        <svg
                                            className={`w-4 h-4 transition-transform duration-200 ${isToolsDropdownOpen ? 'rotate-180' : ''}`}
                                            fill="none"
                                            stroke="currentColor"
                                            viewBox="0 0 24 24"
                                        >
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                                        </svg>
                                    </button>

                                    <AnimatePresence>
                                        {isToolsDropdownOpen && (
                                            <motion.div
                                                initial={{ opacity: 0, y: -10, scale: 0.95 }}
                                                animate={{ opacity: 1, y: 0, scale: 1 }}
                                                exit={{ opacity: 0, y: -10, scale: 0.95 }}
                                                transition={{ duration: 0.2 }}
                                                className="absolute top-full left-0 mt-2 w-48 bg-dark-card/95 backdrop-blur-lg border border-primary/20 rounded-lg shadow-xl z-50"
                                            >
                                                <div className="py-2">
                                                    <Link
                                                        href={"/image-generator" as Pathnames}
                                                        className="block px-4 py-2 text-text-light hover:text-primary hover:bg-primary/10 transition-colors font-title nav-text"
                                                        onClick={() => setIsToolsDropdownOpen(false)}
                                                    >
                                                        {t('nav.toolsDropdown.imageGenerator')}
                                                    </Link>
                                                    <Link
                                                        href={"/avatar-creator" as Pathnames}
                                                        className="block px-4 py-2 text-text-light hover:text-primary hover:bg-primary/10 transition-colors font-title nav-text"
                                                        onClick={() => setIsToolsDropdownOpen(false)}
                                                    >
                                                        {t('nav.toolsDropdown.avatarCreator')}
                                                    </Link>
                                                    <Link
                                                        href={"/image-to-text" as Pathnames}
                                                        className="block px-4 py-2 text-text-light hover:text-primary hover:bg-primary/10 transition-colors font-title nav-text"
                                                        onClick={() => setIsToolsDropdownOpen(false)}
                                                    >
                                                        {t('nav.toolsDropdown.imageToText')}
                                                    </Link>
                                                    <Link
                                                        href={"/imagen" as Pathnames}
                                                        className="block px-4 py-2 text-text-light hover:text-primary hover:bg-primary/10 transition-colors font-title nav-text"
                                                        onClick={() => setIsToolsDropdownOpen(false)}
                                                    >
                                                        {t('nav.toolsDropdown.imagen')}
                                                    </Link>
                                                </div>
                                            </motion.div>
                                        )}
                                    </AnimatePresence>
                                </div>

                                <Link href={"/gallery" as Pathnames} className="text-text-light hover:text-primary transition-colors font-title nav-text">
                                    {t('nav.gallery')}
                                </Link>
                                <Link href={"/about" as Pathnames} className="text-text-light hover:text-primary transition-colors font-title nav-text">
                                    {t('nav.about')}
                                </Link>

                                {/* Authentication UI */}
                                {isLoading ? (
                                    <div className="w-8 h-8 rounded-full bg-primary/20 animate-pulse"></div>
                                ) : isAuthenticated && user ? (
                                    <div className="relative">
                                        <button
                                            className="flex items-center space-x-2 text-text-light hover:text-primary transition-colors"
                                            onClick={() => setIsUserDropdownOpen(!isUserDropdownOpen)}
                                            onBlur={() => setTimeout(() => setIsUserDropdownOpen(false), 150)}
                                        >
                                            {user.image ? (
                                                <Image
                                                    src={user.image}
                                                    alt={user.name || 'User'}
                                                    width={32}
                                                    height={32}
                                                    className="rounded-full"
                                                />
                                            ) : (
                                                <div className="w-8 h-8 rounded-full bg-primary/20 flex items-center justify-center">
                                                    <User className="w-4 h-4" />
                                                </div>
                                            )}
                                            <svg
                                                className={`w-4 h-4 transition-transform duration-200 ${isUserDropdownOpen ? 'rotate-180' : ''}`}
                                                fill="none"
                                                stroke="currentColor"
                                                viewBox="0 0 24 24"
                                            >
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                                            </svg>
                                        </button>

                                        <AnimatePresence>
                                            {isUserDropdownOpen && (
                                                <motion.div
                                                    initial={{ opacity: 0, y: -10, scale: 0.95 }}
                                                    animate={{ opacity: 1, y: 0, scale: 1 }}
                                                    exit={{ opacity: 0, y: -10, scale: 0.95 }}
                                                    transition={{ duration: 0.2 }}
                                                    className="absolute top-full right-0 mt-2 w-48 bg-dark-card/95 backdrop-blur-lg border border-primary/20 rounded-lg shadow-xl z-50"
                                                >
                                                    <div className="py-2">
                                                        <div className="px-4 py-2 border-b border-primary/10">
                                                            <p className="text-sm font-medium text-text-light">{user.name}</p>
                                                            <p className="text-xs text-text-muted">{user.email}</p>
                                                            <div className="flex items-center gap-2 mt-2">
                                                                <Coins className="w-4 h-4 text-accent" />
                                                                <span className="text-sm font-medium text-accent">
                                                                    {creditsLoading ? '...' : credits} Credits
                                                                </span>
                                                            </div>
                                                        </div>

                                                        <Link
                                                            href={"/profile" as Pathnames}
                                                            className="w-full text-left px-4 py-2 text-text-light hover:text-primary hover:bg-primary/10 transition-colors font-title nav-text flex items-center gap-2"
                                                            onClick={() => setIsUserDropdownOpen(false)}
                                                        >
                                                            <User className="w-4 h-4" />
                                                            {t('nav.profile')}
                                                        </Link>

                                                        <Link
                                                            href={"/pricing" as Pathnames}
                                                            className="w-full text-left px-4 py-2 text-text-light hover:text-primary hover:bg-primary/10 transition-colors font-title nav-text flex items-center gap-2"
                                                            onClick={() => setIsUserDropdownOpen(false)}
                                                        >
                                                            <CreditCard className="w-4 h-4" />
                                                            {t('nav.pricing')}
                                                        </Link>

                                                        <div className="border-t border-primary/10 mt-1 pt-1">
                                                            <button
                                                                onClick={() => {
                                                                    signOut();
                                                                    setIsUserDropdownOpen(false);
                                                                }}
                                                                className="w-full text-left px-4 py-2 text-text-light hover:text-primary hover:bg-primary/10 transition-colors font-title nav-text flex items-center gap-2"
                                                            >
                                                                <LogOut className="w-4 h-4" />
                                                                {t('auth.signOut')}
                                                            </button>
                                                        </div>
                                                    </div>
                                                </motion.div>
                                            )}
                                        </AnimatePresence>
                                    </div>
                                ) : (
                                    <button
                                        onClick={() => signIn('google')}
                                        className="flex items-center gap-2 px-4 py-2 bg-primary hover:bg-primary-dark text-white rounded-lg transition-colors font-title nav-text"
                                    >
                                        <LogIn className="w-4 h-4" />
                                        {t('auth.signIn')}
                                    </button>
                                )}
                            </div>
                        </nav>

                        {/* Mobile Menu */}
                        <AnimatePresence>
                            {isMenuOpen && (
                                <motion.div
                                    initial={{ height: 0, opacity: 0 }}
                                    animate={{ height: 'auto', opacity: 1 }}
                                    exit={{ height: 0, opacity: 0 }}
                                    transition={{ duration: 0.3 }}
                                    className="sm:hidden overflow-hidden"
                                >
                                    <div className="flex flex-col py-3 space-y-3 border-t border-primary/10 mt-3">
                                        {/* Mobile Tools Section */}
                                        <div className="space-y-2">
                                            <div className="text-text-light font-title nav-text py-2 px-2 text-sm font-semibold text-primary/80">
                                                {t('nav.tools')}
                                            </div>
                                            <Link
                                                href={"/image-generator" as Pathnames}
                                                className="text-text-light hover:text-primary transition-colors font-title nav-text py-2 px-4 rounded-lg block ml-2"
                                                onClick={() => setIsMenuOpen(false)}
                                            >
                                                {t('nav.toolsDropdown.imageGenerator')}
                                            </Link>
                                            <Link
                                                href={"/avatar-creator" as Pathnames}
                                                className="text-text-light hover:text-primary transition-colors font-title nav-text py-2 px-4 rounded-lg block ml-2"
                                                onClick={() => setIsMenuOpen(false)}
                                            >
                                                {t('nav.toolsDropdown.avatarCreator')}
                                            </Link>
                                            <Link
                                                href={"/image-to-text" as Pathnames}
                                                className="text-text-light hover:text-primary transition-colors font-title nav-text py-2 px-4 rounded-lg block ml-2"
                                                onClick={() => setIsMenuOpen(false)}
                                            >
                                                {t('nav.toolsDropdown.imageToText')}
                                            </Link>
                                            <Link
                                                href={"/imagen" as Pathnames}
                                                className="text-text-light hover:text-primary transition-colors font-title nav-text py-2 px-4 rounded-lg block ml-2"
                                                onClick={() => setIsMenuOpen(false)}
                                            >
                                                {t('nav.toolsDropdown.imagen')}
                                            </Link>
                                        </div>

                                        <Link
                                            href={"/gallery" as Pathnames}
                                            className="text-text-light hover:text-primary transition-colors font-title nav-text py-2 px-2 rounded-lg"
                                            onClick={() => setIsMenuOpen(false)}
                                        >
                                            {t('nav.gallery')}
                                        </Link>
                                        <Link
                                            href={"/about" as Pathnames}
                                            className="text-text-light hover:text-primary transition-colors font-title nav-text py-2 px-2 rounded-lg"
                                            onClick={() => setIsMenuOpen(false)}
                                        >
                                            {t('nav.about')}
                                        </Link>

                                        {/* Mobile Authentication UI */}
                                        <div className="border-t border-primary/10 pt-3 mt-3">
                                            {isLoading ? (
                                                <div className="flex items-center justify-center py-2">
                                                    <div className="w-8 h-8 rounded-full bg-primary/20 animate-pulse"></div>
                                                </div>
                                            ) : isAuthenticated && user ? (
                                                <div className="space-y-2">
                                                    <div className="flex items-center space-x-3 px-2 py-2">
                                                        {user.image ? (
                                                            <Image
                                                                src={user.image}
                                                                alt={user.name || 'User'}
                                                                width={32}
                                                                height={32}
                                                                className="rounded-full"
                                                            />
                                                        ) : (
                                                            <div className="w-8 h-8 rounded-full bg-primary/20 flex items-center justify-center">
                                                                <User className="w-4 h-4" />
                                                            </div>
                                                        )}
                                                        <div className="flex-1">
                                                            <p className="text-sm font-medium text-text-light">{user.name}</p>
                                                            <p className="text-xs text-text-muted">{user.email}</p>
                                                            <div className="flex items-center gap-2 mt-1">
                                                                <Coins className="w-3 h-3 text-accent" />
                                                                <span className="text-xs font-medium text-accent">
                                                                    {creditsLoading ? '...' : credits} Credits
                                                                </span>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <Link
                                                        href={"/profile" as Pathnames}
                                                        className="w-full text-left px-2 py-2 text-text-light hover:text-primary transition-colors font-title nav-text rounded-lg flex items-center gap-2"
                                                        onClick={() => setIsMenuOpen(false)}
                                                    >
                                                        <User className="w-4 h-4" />
                                                        {t('nav.profile')}
                                                    </Link>

                                                    <Link
                                                        href={"/pricing" as Pathnames}
                                                        className="w-full text-left px-2 py-2 text-text-light hover:text-primary transition-colors font-title nav-text rounded-lg flex items-center gap-2"
                                                        onClick={() => setIsMenuOpen(false)}
                                                    >
                                                        <CreditCard className="w-4 h-4" />
                                                        {t('nav.pricing')}
                                                    </Link>

                                                    <div className="border-t border-primary/10 pt-2 mt-2">
                                                        <button
                                                            onClick={() => {
                                                                signOut();
                                                                setIsMenuOpen(false);
                                                            }}
                                                            className="w-full text-left px-2 py-2 text-text-light hover:text-primary transition-colors font-title nav-text rounded-lg flex items-center gap-2"
                                                        >
                                                            <LogOut className="w-4 h-4" />
                                                            {t('auth.signOut')}
                                                        </button>
                                                    </div>
                                                </div>
                                            ) : (
                                                <button
                                                    onClick={() => {
                                                        signIn('google');
                                                        setIsMenuOpen(false);
                                                    }}
                                                    className="w-full flex items-center justify-center gap-2 px-4 py-2 bg-primary hover:bg-primary-dark text-white rounded-lg transition-colors font-title nav-text"
                                                >
                                                    <LogIn className="w-4 h-4" />
                                                    {t('auth.signIn')}
                                                </button>
                                            )}
                                        </div>
                                    </div>
                                </motion.div>
                            )}
                        </AnimatePresence>
                    </div>
                </motion.header>
            )}
        </AnimatePresence>
    );
}