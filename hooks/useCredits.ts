import { useState, useEffect } from 'react'
import { useAuth } from './useAuth'

interface UserCredits {
  credits: number
  loading: boolean
  error: string | null
  refresh: () => Promise<void>
}

export function useCredits(): UserCredits {
  const { user, isAuthenticated } = useAuth()
  const [credits, setCredits] = useState<number>(0)
  const [loading, setLoading] = useState<boolean>(false)
  const [error, setError] = useState<string | null>(null)

  const fetchCredits = async () => {
    if (!isAuthenticated || !user) {
      setCredits(0)
      return
    }

    setLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/credits')
      if (!response.ok) {
        throw new Error('Failed to fetch credits')
      }
      
      const data = await response.json()
      setCredits(data.credits || 0)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error')
      setCredits(0)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchCredits()
  }, [isAuthenticated, user])

  return {
    credits,
    loading,
    error,
    refresh: fetchCredits
  }
}
