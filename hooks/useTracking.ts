import { useCallback } from 'react';
import { notifyFeishu } from '@/utils/notifyFeishu';
import { logEvent } from '@/utils/gaLog';
import { getIpInfo } from '@/utils/ipTracker';
import { TrackingEvent, TrackingCategory } from '@/utils/trackingEvents';

export function useTracking() {
    const trackAction = useCallback(async (
        action: TrackingEvent,
        category: TrackingCategory,
        label: string,
        value: number = 1,
        additionalInfo: Record<string, any> = {}
    ) => {
        // Track in GA (non-blocking)
        Promise.resolve().then(() => {
            logEvent(action, category, label, value);
        });

        // Track in Feishu (non-blocking)
        Promise.resolve().then(async () => {
            try {
                const ipInfo = await getIpInfo();
                const timestamp = new Date().toISOString();
                const userAgent = navigator.userAgent;
                const screenSize = `${window.innerWidth}x${window.innerHeight}`;

                const message = `
                    🎯 User Action
                    Action: ${action}
                    Category: ${category}
                    Label: ${label}
                    Value: ${value}
                    Time: ${timestamp}
                    Path: ${window.location.pathname}
                    IP: ${ipInfo?.ip || 'Unknown'}
                    Location: ${ipInfo?.city || 'Unknown'}, ${ipInfo?.region || ''}, ${ipInfo?.country || ''}
                    Device: ${userAgent}
                    Screen: ${screenSize}
                    Additional Info: ${JSON.stringify(additionalInfo)}
                `.trim();

                await notifyFeishu.notify(message);
            } catch (error) {
                console.error('Failed to track action:', error);
            }
        });
    }, []);

    return { trackAction };
} 