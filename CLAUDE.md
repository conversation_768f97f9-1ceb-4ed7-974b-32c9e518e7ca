# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

- `npm run dev` - Start development server (runs on port 3000)
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npx prisma migrate dev` - Run database migrations
- `npx prisma studio` - Open Prisma Studio for database management
- `./build.sh` - Build and push Docker image
- `./deploy.sh` - Deploy the application

## Architecture Overview

This is a Next.js 15 application with internationalization (i18n) support using next-intl. The app is structured around AI tools for image and video generation.

### Key Architectural Patterns

- **App Router**: Uses Next.js App Router with internationalized routes under `app/[locale]/`
- **Middleware-based Access Control**: Custom middleware (`middleware.ts`) implements IP whitelisting and bot protection
- **Database Layer**: Prisma ORM with PostgreSQL for user management, AI generations, and subscriptions
- **Internationalization**: next-intl with locales in `locales/` directory, supporting English and Chinese
- **Component Structure**: UI components in `components/ui/`, with layout components (<PERSON><PERSON>, Footer)

### Database Schema

The application uses Prisma with these core models:
- `User` - User accounts with credits system
- `AIImage` - Generated images with metadata
- `AIVideo` - Generated videos with metadata  
- `AIAvatar` - Generated avatars
- `Subscription` - Stripe-based subscription management

### Access Control System

The middleware implements:
- IP-based access control via `ALLOWED_IPS` environment variable
- Bot detection and blocking (returns 403 for crawlers)
- Development mode bypass (all IPs allowed in development)
- Redirect to `/blocked` page for unauthorized access

### Styling and UI

- **TailwindCSS**: Custom configuration with brand colors (electric blue #00BFFF, neon purple #A259FF)
- **Typography**: Multi-font system (Inter, Space Grotesk, Plus Jakarta Sans, JetBrains Mono)
- **Theme**: Dark-themed design with gradient backgrounds
- **Responsive**: Mobile-first responsive design

## Environment Configuration

Required environment variables:
- `DATABASE_URL` - PostgreSQL connection string
- `ALLOWED_IPS` - Comma-separated list of allowed IP addresses (supports CIDR notation)
- `NODE_ENV` - Set to 'development' to bypass IP restrictions
- AI service API keys (OpenAI, Stability, Replicate)
- Stripe configuration for payments

## Internationalization

- Default locale: English ('en')
- Supported locales: English ('en'), Chinese ('zh')
- Translation files: `locales/common/` and `locales/pages/`
- Routing: Uses 'as-needed' locale prefix strategy
- Routes are defined in `i18n/routing.ts`

## Security Features

- IP whitelisting with CIDR support
- Bot protection and crawler blocking
- SEO protection headers (noindex, nofollow)
- Content security headers
- No indexing by search engines

## Docker Deployment

The application includes Docker support:
- Multi-stage Dockerfile for production builds
- Build script (`build.sh`) with version tagging
- Deployment script (`deploy.sh`) for container orchestration
- Supports multi-platform builds (linux/amd64)