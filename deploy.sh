#!/bin/bash

# 设置错误处理
set -e

# 获取脚本所在目录的绝对路径（也是项目根目录）
PROJECT_ROOT="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"

# 切换到项目根目录
cd "$PROJECT_ROOT"

# 设置默认项目名称，如果 package.json 不存在也能工作
DEFAULT_PROJECT_NAME="a3i"

# 尝试从 package.json 获取项目名称，如果失败则使用默认值
if [ -f "package.json" ]; then
    PROJECT_NAME=$(grep -m 1 '"name":' package.json | cut -d '"' -f 4)
    if [ -z "$PROJECT_NAME" ]; then
        echo "警告：无法从 package.json 获取项目名称，使用默认名称：$DEFAULT_PROJECT_NAME"
        PROJECT_NAME=$DEFAULT_PROJECT_NAME
    fi
else
    echo "警告：package.json 不存在，使用默认项目名称：$DEFAULT_PROJECT_NAME"
    PROJECT_NAME=$DEFAULT_PROJECT_NAME
fi

# 检查生产环境配置文件
if [ ! -f "docker-compose.prod.yml" ]; then
    echo "错误：找不到 docker-compose.prod.yml 文件"
    exit 1
fi

# 检查环境变量文件
if [ ! -f ".env.production" ]; then
    echo "警告：.env.production 文件不存在，可能导致配置问题"
    
    # 提供创建基本 .env.production 文件的选项
    echo "是否创建基本的 .env.production 文件？[y/N]"
    read -r response
    if [[ "$response" =~ ^[Yy]$ ]]; then
        echo "创建基本的 .env.production 文件..."
        cat > .env.production << EOL
# 应用配置
NODE_ENV=production
NEXT_PUBLIC_API_URL=https://api.domain.com
NEXT_PUBLIC_SITE_URL=https://domain.com

# 数据库配置
DATABASE_URL=***************************************************/a3i
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres_password
POSTGRES_DB=a3i

# 认证配置
NEXTAUTH_URL=https://domain.com
NEXTAUTH_SECRET=replace_with_strong_secret

# API 安全
API_SECRET_KEY=replace_with_strong_api_key

# 分析和监控
NEXT_PUBLIC_GA_ID=
NEXT_PUBLIC_FEISHU_NOTIFY_WEBHOOK_URL=
NEXT_PUBLIC_FEISHU_KEY_WEBHOOK_URL=
EOL
        echo ".env.production 文件已创建，请在部署前修改其中的值"
    fi
fi

# 尝试从 docker-compose.prod.yml 提取服务名称（更可靠的方法）
# 使用 grep 和 sed 提取第一个服务名称
SERVICE_NAME=$(grep -A1 "services:" docker-compose.prod.yml | grep -v "services:" | sed 's/[[:space:]]*\([^:]*\):.*/\1/')
if [ -n "$SERVICE_NAME" ]; then
    echo "从 docker-compose.prod.yml 检测到服务名称：$SERVICE_NAME"
    PROJECT_NAME=$SERVICE_NAME
fi

echo "使用项目/服务名称：$PROJECT_NAME"

# 检查数据库服务名称
DB_SERVICE_NAME="${PROJECT_NAME}_db"
if grep -q "$DB_SERVICE_NAME" docker-compose.prod.yml; then
    echo "检测到数据库服务名称：$DB_SERVICE_NAME"
else
    echo "警告：未检测到预期的数据库服务名称 $DB_SERVICE_NAME，数据库备份可能失败"
    # 尝试查找任何包含 db 的服务
    DB_SERVICE_NAME=$(grep -o "[a-zA-Z0-9_]*db[a-zA-Z0-9_]*:" docker-compose.prod.yml | sed 's/://')
    if [ -n "$DB_SERVICE_NAME" ]; then
        echo "找到可能的数据库服务：$DB_SERVICE_NAME"
    fi
fi

# 检查关键环境变量
if [ -f ".env.production" ]; then
    # 检查关键环境变量是否在 .env.production 中设置
    if ! grep -q "DATABASE_URL" .env.production; then
        echo "警告：.env.production 中缺少 DATABASE_URL 配置"
    fi
    if ! grep -q "POSTGRES_PASSWORD" .env.production; then
        echo "警告：.env.production 中缺少 POSTGRES_PASSWORD 配置"
    fi
    
    # 检查是否使用了默认密码
    if grep -q "POSTGRES_PASSWORD=postgres_password" .env.production; then
        echo "警告：.env.production 中使用了默认数据库密码，建议更改为安全密码"
    fi
    if grep -q "NEXTAUTH_SECRET=replace_with_strong_secret" .env.production; then
        echo "警告：.env.production 中使用了默认 NEXTAUTH_SECRET，建议更改为安全密钥"
    fi
fi

# 检查 Docker 是否运行
if ! docker info > /dev/null 2>&1; then
    echo "错误：Docker 未运行或无法访问 Docker 守护进程"
    echo "请确保 Docker 已安装并正在运行"
    exit 1
fi

# 备份数据库（如果可能）
if [ -n "$DB_SERVICE_NAME" ]; then
    echo "正在尝试备份数据库..."
    BACKUP_DATE=$(date +"%Y%m%d_%H%M%S")
    BACKUP_DIR="backups"
    mkdir -p "$BACKUP_DIR"
    BACKUP_FILE="$BACKUP_DIR/backup_${PROJECT_NAME}_${BACKUP_DATE}.sql"
    
    # 尝试从环境变量文件获取数据库用户和名称
    DB_USER=$(grep "POSTGRES_USER" .env.production | cut -d '=' -f2 | tr -d '"' || echo "postgres")
    DB_NAME=$(grep "POSTGRES_DB" .env.production | cut -d '=' -f2 | tr -d '"' || echo "$PROJECT_NAME")
    
    # 检查容器是否运行
    if docker compose -f docker-compose.prod.yml ps | grep -q "$DB_SERVICE_NAME.*Up"; then
        if docker compose -f docker-compose.prod.yml exec -T $DB_SERVICE_NAME pg_dump -U $DB_USER $DB_NAME > $BACKUP_FILE 2>/dev/null; then
            echo "数据库备份成功：$BACKUP_FILE"
            # 保留最近 5 个备份
            ls -t $BACKUP_DIR/backup_${PROJECT_NAME}_*.sql | tail -n +6 | xargs -r rm
        else
            echo "警告：数据库备份失败，继续部署..."
        fi
    else
        echo "数据库容器未运行，跳过备份"
    fi
fi

# 记录当前镜像版本，用于可能的回滚
CURRENT_IMAGE=$(docker compose -f docker-compose.prod.yml images $PROJECT_NAME --quiet 2>/dev/null)
if [ -n "$CURRENT_IMAGE" ]; then
    echo "当前镜像 ID: $CURRENT_IMAGE"
fi

# 拉取最新的 Docker 镜像
echo "正在拉取最新的 Docker 镜像..."
if docker compose -f docker-compose.prod.yml pull $PROJECT_NAME; then
    echo "Docker 镜像拉取成功。"
else
    echo "Docker 镜像拉取失败！"
    echo "请检查 Docker Hub 凭据和网络连接"
    exit 1
fi

# 停止 Docker 容器
echo "正在停止 Docker 容器..."
if docker compose -f docker-compose.prod.yml stop $PROJECT_NAME; then
    echo "Docker 容器停止成功。"
else
    echo "警告：Docker 容器停止失败，可能容器不存在或已停止"
fi

# 启动 Docker 容器
echo "正在启动 Docker 容器..."
if docker compose -f docker-compose.prod.yml up $PROJECT_NAME -d; then
    echo "Docker 容器启动成功。"
else
    echo "Docker 容器启动失败！"
    
    # 如果有之前的镜像，提供回滚选项
    if [ -n "$CURRENT_IMAGE" ]; then
        echo "是否回滚到之前的版本？[y/N]"
        read -r response
        if [[ "$response" =~ ^[Yy]$ ]]; then
            echo "正在回滚..."
            # 尝试使用之前的镜像重新启动
            if docker compose -f docker-compose.prod.yml up $PROJECT_NAME -d; then
                echo "回滚成功。"
            else
                echo "回滚失败！"
                exit 1
            fi
        else
            exit 1
        fi
    else
        exit 1
    fi
fi

# 等待容器完全启动
echo "等待容器完全启动（15秒）..."
sleep 15

# 检查容器是否健康
HEALTH_CHECK_ATTEMPTS=0
MAX_ATTEMPTS=5
CONTAINER_ID=$(docker compose -f docker-compose.prod.yml ps -q $PROJECT_NAME)

if [ -n "$CONTAINER_ID" ]; then
    echo "检查容器健康状态..."
    while [ $HEALTH_CHECK_ATTEMPTS -lt $MAX_ATTEMPTS ]; do
        HEALTH_STATUS=$(docker inspect --format='{{.State.Health.Status}}' $CONTAINER_ID 2>/dev/null || echo "无健康检查")
        
        if [ "$HEALTH_STATUS" = "healthy" ]; then
            echo "容器健康状态: 健康"
            break
        elif [ "$HEALTH_STATUS" = "无健康检查" ]; then
            echo "容器没有配置健康检查，跳过检查"
            break
        else
            echo "容器健康状态: $HEALTH_STATUS，等待 10 秒后重试..."
            ((HEALTH_CHECK_ATTEMPTS++))
            sleep 10
        fi
    done
    
    if [ $HEALTH_CHECK_ATTEMPTS -eq $MAX_ATTEMPTS ] && [ "$HEALTH_STATUS" != "healthy" ] && [ "$HEALTH_STATUS" != "无健康检查" ]; then
        echo "警告：容器未能达到健康状态，但将继续部署"
    fi
fi

# 运行数据库迁移
echo "正在运行数据库迁移..."
if docker compose -f docker-compose.prod.yml exec -T $PROJECT_NAME npx prisma migrate deploy; then
    echo "数据库迁移成功。"
else
    echo "警告：数据库迁移失败！应用可能无法正常工作。"
    echo "请手动检查数据库状态。"
    
    # 提供选项继续或回滚
    echo "是否继续部署？[Y/n]"
    read -r response
    if [[ "$response" =~ ^[Nn]$ ]]; then
        echo "正在回滚..."
        if [ -n "$CURRENT_IMAGE" ]; then
            # 尝试使用之前的镜像重新启动
            if docker compose -f docker-compose.prod.yml up $PROJECT_NAME -d; then
                echo "回滚成功。"
            else
                echo "回滚失败！"
            fi
        else
            echo "无法回滚，没有之前的镜像信息。"
        fi
        exit 1
    fi
fi

# 获取服务器 IP 和域名信息
SERVER_IP=$(hostname -I | awk '{print $1}')
DOMAIN_NAME=$(grep "NEXT_PUBLIC_SITE_URL" .env.production 2>/dev/null | cut -d '=' -f2 | tr -d '"' | sed 's|https://||' || echo "domain.com")

echo "所有操作成功完成。"
echo "应用已部署并运行在："
echo "- 本地访问: http://$SERVER_IP:3000"
echo "- 域名访问: https://$DOMAIN_NAME (如果已配置 DNS)"

# 显示日志访问提示
echo -e "\n要查看应用日志，请运行:"
echo "docker compose -f docker-compose.prod.yml logs -f $PROJECT_NAME"
