import { getRequestConfig } from 'next-intl/server';
import { routing } from './routing';
import { localePaths } from '../locales';

export default getRequestConfig(async ({ requestLocale }) => {
    // This typically corresponds to the `[locale]` segment
    let locale = await requestLocale;

    // Ensure that the incoming `locale` is valid
    if (!locale || !routing.locales.includes(locale as any)) {
        locale = routing.defaultLocale;
    }

    // Load and merge message files for this locale
    const loadMessages = async (locale: string) => {
        // Common translations
        const common = (await import(`../locales/${localePaths.common}/${locale}.json`)).default;

        // Page-specific translations using path configurations from locales/index.ts
        const home = (await import(`../locales/${localePaths.pages.home}/${locale}.json`)).default;
        const about = (await import(`../locales/${localePaths.pages.about}/${locale}.json`)).default;
        const gallery = (await import(`../locales/${localePaths.pages.gallery}/${locale}.json`)).default;
        const imagen = (await import(`../locales/${localePaths.pages.imagen}/${locale}.json`)).default;
        const imageGenerator = (await import(`../locales/${localePaths.pages.imageGenerator}/${locale}.json`)).default;
        const avatarCreator = (await import(`../locales/${localePaths.pages.avatarCreator}/${locale}.json`)).default;
        const imageToText = (await import(`../locales/${localePaths.pages.imageToText}/${locale}.json`)).default;
        const profile = (await import(`../locales/${localePaths.pages.profile}/${locale}.json`)).default;
        const pricing = (await import(`../locales/${localePaths.pages.pricing}/${locale}.json`)).default;
        const blocked = (await import(`../locales/${localePaths.pages.blocked}/${locale}.json`)).default;
        const notFound = (await import(`../locales/${localePaths.pages.notFound}/${locale}.json`)).default;
        const privacy = (await import(`../locales/${localePaths.pages.privacy}/${locale}.json`)).default;
        const terms = (await import(`../locales/${localePaths.pages.terms}/${locale}.json`)).default;

        // Merge all translations
        return {
            ...common,
            pages: {
                home,
                about,
                gallery,
                imagen,
                imageGenerator,
                avatarCreator,
                imageToText,
                profile,
                pricing,
                blocked,
                notFound,
                privacy,
                terms
            }
        };
    };

    // Return the configuration
    return {
        locale,
        messages: await loadMessages(locale)
    };
});