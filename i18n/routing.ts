import { createNavigation } from 'next-intl/navigation';
import { defineRouting } from 'next-intl/routing';
import { supportedLocales, defaultLocale, localeNames } from '../locales';

export const locales = supportedLocales;

export const routing = defineRouting({
    locales,
    defaultLocale,
    localeDetection: false,
    localePrefix: 'as-needed',
    pathnames: {
        '/': '/',
        '/about': '/about',
        '/gallery': '/gallery',
        '/imagen': '/imagen',
        '/image-generator': '/image-generator',
        '/avatar-creator': '/avatar-creator',
        '/image-to-text': '/image-to-text',
        '/privacy-policy': '/privacy-policy',
        '/terms-of-service': '/terms-of-service',
        // 404 page is handled automatically
    }
});

export type Pathnames = keyof typeof routing.pathnames;

export const { Link, redirect, usePathname, useRouter } = createNavigation(routing);

export type Locale = (typeof locales)[number];

export const fullLanguageNames = localeNames;