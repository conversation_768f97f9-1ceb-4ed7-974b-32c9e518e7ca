import { NextRequest, NextResponse } from 'next/server';
import createIntlMiddleware from 'next-intl/middleware';
import { routing } from './i18n/routing';

// 配置允许访问的IP地址白名单
// 例如: ['127.0.0.1', '***********']
// 使用 CIDR 表示法可以指定 IP 范围，例如 '***********/24'
const ALLOWED_IPS: string[] = process.env.ALLOWED_IPS ? process.env.ALLOWED_IPS.split(',') : [];
const DEVELOPMENT_MODE = process.env.NODE_ENV === 'development';

// 检查IP是否在白名单中
function isIpAllowed(ip: string): boolean {
    // 开发模式下允许所有IP访问
    if (DEVELOPMENT_MODE) {
        return true;
    }

    // 如果白名单为空，不限制IP访问
    if (ALLOWED_IPS.length === 0) {
        return true;
    }

    return ALLOWED_IPS.some(allowedIp => {
        // 简单的IP匹配
        if (allowedIp === ip) {
            return true;
        }

        // CIDR匹配 (简化版)
        if (allowedIp.includes('/')) {
            try {
                const [subnet, bits] = allowedIp.split('/');
                const mask = parseInt(bits, 10);
                const ipParts = ip.split('.').map(part => parseInt(part, 10));
                const subnetParts = subnet.split('.').map(part => parseInt(part, 10));

                // 转换为二进制进行比较
                const ipBin = ipParts.reduce((acc, octet) => (acc << 8) + octet, 0) >>> 0;
                const subnetBin = subnetParts.reduce((acc, octet) => (acc << 8) + octet, 0) >>> 0;
                const maskBin = 0xffffffff << (32 - mask);

                return (ipBin & maskBin) === (subnetBin & maskBin);
            } catch (e) {
                console.error(`Invalid CIDR notation: ${allowedIp}`);
                return false;
            }
        }

        return false;
    });
}

// 创建国际化中间件
const intlMiddleware = createIntlMiddleware(routing);

// 自定义中间件
export async function middleware(request: NextRequest) {
    // 如果用户已经在访问 blocked 页面，不进行 IP 检查，直接展示
    if (request.nextUrl.pathname.includes('/blocked')) {
        return intlMiddleware(request);
    }

    // 获取客户端IP
    const forwarded = request.headers.get('x-forwarded-for');
    const ip = forwarded ? forwarded.split(',')[0] : '127.0.0.1';

    // 检查是否为爬虫
    const userAgent = request.headers.get('user-agent') || '';
    const isBot = /bot|crawler|spider|crawling/i.test(userAgent);

    // 如果是爬虫或IP不在白名单中
    if (isBot || !isIpAllowed(ip)) {
        // 针对爬虫直接返回403
        if (isBot) {
            const response = new NextResponse('Access Denied', {
                status: 403,
                headers: {
                    'Content-Type': 'text/plain',
                },
            });

            // 添加禁止爬虫的头部
            response.headers.set('X-Robots-Tag', 'noindex, nofollow, noarchive');
            response.headers.set('X-Content-Type-Options', 'nosniff');

            return response;
        }

        // 针对IP不在白名单的用户，重定向到 blocked 页面
        const locale = request.nextUrl.pathname.split('/')[1] || 'en';
        const blockedUrl = new URL(`/${locale}/blocked`, request.url);

        return NextResponse.redirect(blockedUrl);
    }

    // 添加安全和禁止爬虫头部到所有响应
    const response = await intlMiddleware(request);
    response.headers.set('X-Robots-Tag', 'noindex, nofollow, noarchive');
    response.headers.set('X-Content-Type-Options', 'nosniff');

    return response;
}

export const config = {
    matcher: ['/((?!api|_next|.*\\..*).*)']
};
