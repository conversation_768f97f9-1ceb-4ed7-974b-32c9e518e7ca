import type { Config } from "tailwindcss";

const config: Config = {
  content: [
    "./pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      fontFamily: {
        sans: [
          'Inter',
          'Noto Sans SC',
          'ui-sans-serif',
          'system-ui',
          'sans-serif'
        ],
        display: [
          'Space Grotesk',
          'Satoshi',
          'Inter',
          'sans-serif'
        ],
        title: [
          'Plus Jakarta Sans',
          'Satoshi',
          'Noto Sans SC',
          'sans-serif'
        ],
        mono: [
          'JetBrains Mono',
          'ui-monospace',
          'monospace'
        ]
      },
      colors: {
        primary: {
          DEFAULT: "#00BFFF", // Electric blue
          dark: "#0099CC",
        },
        secondary: {
          DEFAULT: "#A259FF", // Neon purple
        },
        accent: {
          DEFAULT: "#FFD700", // Brand yellow
        },
        background: {
          DEFAULT: "#ffffff",
          dark: "#0F0F1A", // Deep dark blue
        },
        dark: {
          DEFAULT: "#121212",
          lighter: "#1E1E2D",
          card: "#1A1A2E",
        },
        text: {
          DEFAULT: "#333333",
          light: "#EAEAEA",
          muted: "#888888",
        }
      },
      typography: {
        DEFAULT: {
          css: {
            maxWidth: 'none',
            color: 'rgb(55 65 81)',
            a: {
              color: '#00BFFF',
              '&:hover': {
                color: '#0099CC',
              },
            },
          },
        },
      },
      backgroundImage: {
        'gradient-primary': 'linear-gradient(90deg, #00BFFF 0%, #A259FF 100%)',
        'gradient-secondary': 'linear-gradient(90deg, #A259FF 0%, #FF6B81 100%)',
        'gradient-accent': 'linear-gradient(90deg, #FFD700 0%, #FF6B81 100%)',
      },
    },
  },
  plugins: [
    require('@tailwindcss/typography'),
  ],
};

export default config;
