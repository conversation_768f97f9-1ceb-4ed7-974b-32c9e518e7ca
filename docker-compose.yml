version: '3.8'

services:
  a3i:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        APP_ENV: development
    container_name: a3i
    ports:
      - "3000:3000"
    environment:
      NODE_ENV: development
    env_file:
      - .env
    volumes:
      - .:/app
      - /app/node_modules
      - /app/.next
    depends_on:
      - a3i_db
    restart: unless-stopped

  a3i_db:
    image: postgres:alpine
    ports:
      - "5434:5432"
    env_file:
      - .env
    volumes:
      - postgres_data:/var/lib/postgresql/data
    container_name: a3i_db
    restart: unless-stopped

volumes:
  postgres_data:
