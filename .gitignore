# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js
.yarn/install-state.gz
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.pnp.*
package-lock.json

# testing
/coverage
__tests__/**/coverage

# next.js
/.next/
/out/
/build
.next/
next-env.d.ts

# production
/build
/dist

# environment variables
.env
.env.*
.env.local
.env.*.local
!.env.example
!.env.development

# misc
.DS_Store
*.pem
.idea/
.vscode/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# generated files
/public/generated

# backup
/backups

# prisma
/prisma/dev.db
/prisma/dev.db-journal

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# docker
*.prod.yml
docker-compose.override.yml
docker-compose.prod.local.yml
test-prod.sh
/data
/postgres_data
/postgres_data_prod

# logs
logs
*.log

# cache
.cache
.temp
.tmp

# credentials
google_application_credentials.json
*.key
*.crt
*.cer
*.p12