generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = "file:./dev.db"
}

// NextAuth.js required models
model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model User {
  id            String    @id @default(cuid())
  name          String?
  email         String    @unique
  emailVerified DateTime?
  image         String?
  credits       Int       @default(10) // 新用户默认获得10个credits
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  accounts      Account[]
  sessions      Session[]
  transactions  CreditTransaction[]
  subscriptions Subscription[]
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

// Credits 交易记录
model CreditTransaction {
  id          String              @id @default(cuid())
  userId      String
  type        String              // PURCHASE, USAGE, BONUS, REFUND
  amount      Int                 // 正数为充值，负数为消费
  description String?             // 交易描述
  reference   String?             // 关联的订单ID或操作ID
  createdAt   DateTime            @default(now())

  user        User                @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([createdAt])
}

// 订阅计划
model Subscription {
  id            String            @id @default(cuid())
  userId        String
  planId        String            // 计划ID (basic, pro, premium)
  status        String            // ACTIVE, CANCELLED, EXPIRED, PENDING
  credits       Int               // 该订阅包含的credits数量
  price         Float             // 价格
  currency      String            @default("USD")
  billingCycle  String            // MONTHLY, YEARLY
  stripeId      String?           // Stripe订阅ID
  createdAt     DateTime          @default(now())
  updatedAt     DateTime          @updatedAt
  expiresAt     DateTime?         // 订阅到期时间

  user          User              @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([status])
}


